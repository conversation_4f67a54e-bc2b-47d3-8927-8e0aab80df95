// 评价系统相关接口

// ETC消费评价接口组
const etcEvaluation = {
	'addEtcEvaluation': { // 客户端添加ETC消费评价
		url: '/mp2c',
		method: '/reviews/etc'
	},
	'replyEtcEvaluation': { // 客户端回复ETC消费评价
		url: '/mp2c',
		method: '/reviews/etc/reply'
	},
	'getEtcEvaluationList': { // 根据订单编号查看ETC消费评价列表
		url: '/mp2c',
		method: '/reviews/etc/list'
	},
	'deleteEtcEvaluation': { // 客户端删除ETC消费评价
		url: '/mp2c',
		method: '/reviews/etc'
	}
}

// 一键救援评价接口组
const rescueEvaluation = {
	'addRescueEvaluation': { // 添加一键救援评价
		url: '/mp2c',
		method: '/reviews/rescue'
	},
	'replyRescueEvaluation': { // 回复一键救援评价
		url: '/mp2c',
		method: '/reviews/rescue/reply'
	},
	'getRescueEvaluationList': { // 查看一键救援评价列表
		url: '/mp2c',
		method: '/reviews/rescue/list'
	},
	'deleteRescueEvaluation': { // 删除一键救援评价
		url: '/mp2c',
		method: '/reviews/rescue'
	}
}

// 充电站评价接口组
const chargingStationEvaluation = {
	'addChargingStationEvaluation': { // 客户端添加充电站评价
		url: '/mp2c',
		method: '/reviews/charge-station'
	},
	'replyChargingStationEvaluation': { // 客户端回复充电站评价
		url: '/mp2c',
		method: '/reviews/charge-station/reply'
	},
	'getChargingStationEvaluationList': { // 获取充电站评价列表
		url: '/mp2c',
		method: '/reviews/charge-station/page'
	},
	'deleteChargingStationEvaluation': { // 删除充电站评价
		url: '/mp2c',
		method: '/reviews/charge-station/delete'
	}
}

// 收费站评价接口组
const tollStationEvaluation = {
	'addTollStationEvaluation': { // 客户端添加收费站评价
		url: '/mp2c',
		method: '/reviews/toll'
	},
	'replyTollStationEvaluation': { // 客户端回复收费站评价
		url: '/mp2c',
		method: '/reviews/toll/reply'
	},
	'getTollStationEvaluationList': { // 获取收费站评价列表
		url: '/mp2c',
		method: '/reviews/toll-station/page'
	},
	'deleteTollStationEvaluation': { // 删除收费站评价
		url: '/mp2c',
		method: '/reviews/toll-station/delete'
	}
}

// 服务区评价接口组
const serviceAreaEvaluation = {
	'addServiceAreaEvaluation': { // 客户端添加服务区评价
		url: '/mp2c',
		method: '/reviews/service-area'
	},
	'replyServiceAreaEvaluation': { // 客户端回复服务区评价
		url: '/mp2c',
		method: '/reviews/service-area/reply'
	},
	'getServiceAreaEvaluationList': { // 获取服务区评价列表
		url: '/mp2c',
		method: '/reviews/service-area/page'
	},
	'deleteServiceAreaEvaluation': { // 删除服务区评价
		url: '/mp2c',
		method: '/reviews/service-area/delete'
	}
}

// 用户信息相关接口
const userInfo = {
	'maintainWechatUserInfo': { // 维护微信用户信息
		url: '/mp2c',
		method: '/reviews/wechat/user'
	},
	'getWechatUserInfo': { // 获取微信用户信息
		url: '/mp2c',
		method: '/reviews/get/wechat/user'
	}
}

// 评价配置相关接口
const evaluationConfig = {
	'getEvaluationConfig': { // 查询评价配置
		url: '/mp2c',
		method: '/reviews/config/detail'
	},
	'getImageUploadAuth': { // 获取图片上传地址和凭证
		url: '/mp2c',
		method: '/reviews/image-upload-auth'
	}
}

// 主要评价接口对象
const evaluationAPI = {
	...etcEvaluation,
	...rescueEvaluation,
	...chargingStationEvaluation,
	...tollStationEvaluation,
	...serviceAreaEvaluation,
	...userInfo,
	...evaluationConfig
}

export default evaluationAPI; 