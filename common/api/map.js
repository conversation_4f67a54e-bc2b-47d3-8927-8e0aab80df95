// 公众出行相关接口
const mapAPI = {
	//用户消息查询接口
	'messageList': {
		url: "/mp2c",
		method: '/travel/messageList'
	},
	//用户菜单查询接口
	'userMenuList': {
		url: "/mp2c",
		method: '/travel/userMenuList'
	},
	//用户菜单新增接口
	'addMenu': {
		url: "/mp2c",
		method: '/travel/addMenu'
	},
	//用户菜单删除接口
	'deleteMenu': {
		url: "/mp2c",
		method: '/travel/deleteMenu'
	},
	//服务区数据查询
	'serviceAreaList': {
		url: "/mp2c",
		method: '/travel/serviceAreaList'
	},
	//服务区基本数据详情查询
	'serviceAreaDetail': {
		url: "/mp2c",
		method: '/travel/serviceAreaDetail'
	},
	//服务区收藏
	'addCollect': {
		url: "/mp2c",
		method: '/travel/addCollect'
	},
	//服务区取消收藏
	'delCollect': {
		url: "/mp2c",
		method: '/travel/delCollect'
	},
	//服务区收藏查询
	'getCollectList': {
		url: "/mp2c",
		method: '/travel/collectList'
	},
	//景区列表
	'getAttractionList': {
		url: "/mp2c",
		method: '/advTransfer/attractionList'
	},
	//景区收藏
	'attractionModify': {
		url: "/mp2c",
		method: '/advTransfer/attractionModify'
	},
	//获取首页最近的6条路况信息
	'getHomeMapInfo': {
		url: "/mp2c",
		method: '/advTransfer/getHomeMapInfo'
	},
	//获取所有的优惠路段
	'getMapRoad': {
		url: "/mp2c",
		method: '/travel/queryDTS'
	},
	//应急事件数据查询
	'getMapEvent': {
		url: "/mp2c",
		method: '/travel/emergencyEventsQuery'
	},
	//涉路施工数据查询
	'getMapWork': {
		url: "/mp2c",
		method: '/travel/constructionRoadQuery'
	},
	//拥堵告警数据查询
	'getMapJam': {
		url: "/mp2c",
		method: '/travel/congestionEventsQuery'
	},
	//提交救援申请
	'eventHis': {
		url: "/mp2c",
		method: '/travel/rescue/eventHis'
	},
	//取消订单接口
	'travelCancelOrder': {
		url: "/mp2c",
		method: '/travel/rescue/cancelOrder'
	},
	//吊车收费金额查询
	'craneChargesGet': {
		url: "/mp2c",
		method: '/travel/rescue/craneChargesGet'
	},
	//吊车收费标准查询
	'craneChargesList': {
		url: "/mp2c",
		method: '/travel/rescue/craneChargesList'
	},
	//吊车收费标准详情
	'craneChargesQuery': {
		url: "/mp2c",
		method: '/travel/rescue/craneChargesQuery'
	},
	//拖车收费金额查询
	'getTrailerCharges': {
		url: "/mp2c",
		method: '/travel/rescue/getTrailerCharges'
	},
	//拖车收费标准查询
	'getTrailerCharges': {
		url: "/mp2c",
		method: '/travel/rescue/trailerChargesList'
	},
	//拖车收费标准详情
	'trailerChargesQuery': {
		url: "/mp2c",
		method: '/travel/rescue/trailerChargesQuery'
	},		
	//故障车况查询
	'faultyVehicleCondition': {
		url: "/mp2c",
		method: '/travel/rescue/faultyVehicleCondition'
	},
	//故障车型查询
	'faultyVehicleType': {
		url: "/mp2c",
		method: '/travel/rescue/faultyVehicleType'
	},
	//根据路段id获取路段方向列表
	'getByExpWayId': {
		url: "/mp2c",
		method: '/travel/rescue/getByExpWayId'
	},
	//订单详情接口
	'getDetailByOrderId': {
		url: "/mp2c",
		method: '/travel/rescue/getDetailByOrderId'
	},
	//订单跟踪接口
	'getOrderStatusById': {
		url: "/mp2c",
		method: '/travel/rescue/getOrderStatusById'
	},
	//订单分页列表接口
	'travelGetPageList': {
		url: "/mp2c",
		method: '/travel/rescue/getPageList'
	},
	//获取车牌归属地
	'getPlaceByCode': {
		url: "/mp2c",
		method: '/travel/rescue/getPlaceByCode'
	},
	//根据订单ID获取获取救援车位置信息
	'getRescueCarMsg': {
		url: "/mp2c",
		method: '/travel/rescue/getRescueCarMsg'
	},
	//小程序获取token接口
	'rescueLogin': {
		url: "/mp2c",
		method: '/travel/rescue/login'
	},
	//根据当前经纬度获取路段信息
	'matchPilePointByLonLat': {
		url: "/mp2c",
		method: '/travel/rescue/matchPilePointByLonLat'
	},
	//图片上传接口
	'travelUploadFile': {
		url: "/mp2c",
		method: '/travel/rescue/uploadFile'
	},
	//充电站信息查询接口
	'getChargingStations': {
		url: "/mp2c",
		method: '/chargeStationApi/info'
	},
	//充电站信息详情接口
	'getChargingStationDetail': {
		url: "/mp2c",
		method: '/chargeStationApi/detail'
	},
	//充电站电费详情接口
	'electricDetail': {
		url: "/mp2c",
		method: '/chargeStationApi/electricDetail'
	},
}
export default mapAPI;