<template>
	<view class="high-service">
		<highSearch :placeholder="'请输入服务区名称'" @click="search">
			<template>
				<view class="right-item" @click="go">
					<view style="color: #0066E9;">旅岛优选，</view>
					<view>最新活动戳我</view>
					<image class="right-icon"
						src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/point_icon.png"
						mode=""></image>
				</view>
			</template>
		</highSearch>
		<view v-if="text" class="tips-text">
			{{text}}
		</view>
		<scroll-view class="item-wrapper" :style="{height:height}" scroll-y="true" :scroll-top="scrollTop"
			:lower-threshold='lowerThreshold' @scrolltoupper="upper" @scrolltolower="scrolltolower" @scroll="scroll">
			<seriveItem @star="star" v-for="(item,index) in netList" :item="item" :index="index + 1" :key="index">
			</seriveItem>
			<load-more v-if="!name" :loadStatus="noticeLoadStatus" />
		</scroll-view>
	</view>
</template>
<script>
	import highSearch from '../component/highSearch'
	import seriveItem from './serivceItem.vue'
	import loadMore from '@/pages/home/<USER>/load-more/index.vue';
	import {
		getLoginUserInfo,
		getTicket,
	} from '@/common/storageUtil.js'
	export default {
		components: {
			highSearch,
			seriveItem,
			loadMore
		},
		data() {
			return {
				showStarFlag: false,
				loadFlag: false,
				lowerThreshold: 120,
				scrollTop: 0,
				noticeLoadStatus: 3,
				old: {
					scrollTop: 0
				},
				text: '',
				longitude: '',
				latitude: '',
				netList: [],
				height: '900rpx',
				pageNo: 1,
				pageSize: 20,
				name: '',
				// showDistance: true
			};
		},
		created() {
			this.getLocal()
		},
		methods: {
			upper: function(e) {

			},
			scrolltolower: function(e) {
				console.log('this.noticeLoadStatus ', this.noticeLoadStatus)
				if (this.noticeLoadStatus == 3 || this.loadFlag || this.name) return;
				let self = this;
				setTimeout(function() {
					if (self.noticeLoadStatus != 2) {
						self.pageNo = self.pageNo + 1;
					}
					self.getNetList();
				}, 500)
			},
			scroll: function(e) {
				// this.old.scrollTop = e.detail.scrollTop;
			},
			getNetList(name) {
				this.name = name
				if (!this.longitude || !this.latitude) {
					this.text = '获取不到当前位置信息，无法加载服务区列表'
					return
				}
				this.noticeLoadStatus = 1;
				this.loadFlag = true
				// this.text = '加载列表中...' //
				// console.log('转换前====>>>>', this.longitude, this.latitude)
				// let changeObj = this.qqMapTransBMap(this.longitude, this.latitude)
				// console.log('转换后====>>>>', changeObj.longitude, changeObj.latitude)
				let params = {
					name: name || '',
					longitude: this.longitude,
					latitude: this.latitude,
					pageNo: this.pageNo,
					pageSize: this.pageSize
				}
				this.$request
					.post(this.$interfaces.serviceAreaList, {
						data: params
					})
					.then((res) => {
						this.loadFlag = false
						console.log('服务区数据=====>>>>>', res.data)
						if (res.code == 200) {
							if (res.data.length > 0) {
								this.noticeLoadStatus = 4
								if (this.pageNo == 1) {
									this.netList = res.data
								} else {
									this.netList = this.netList.concat(res.data)
								}
								// this.calculateDistance(res.data)
								//设置条目状态
								this.setListFlag(this.netList)
								//设置收藏状态
								if (getTicket()) {
									//登录过直接加载收藏信息
									this.getCollectList()
								}
								this.text = '' //重置错误信息
								if (this.netList.length == 1) {
									//计算少量数据时滑动高度
									this.height = '280rpx'
								} else if (this.netList.length == 2) {
									this.height = '560rpx'
								} else {
									this.height = '840rpx'
								}
							} else {
								// this.netList = res.data || []
								// if (this.pageNo == 1) {
								// 	this.text = '暂无数据'
								// } else {
								this.noticeLoadStatus = 3;
								// }
							}
						} else {
							if (this.pageNo != 1) {
								this.noticeLoadStatus = 2;
							}
							// this.text = '抱歉，加载列表失败，请稍后重试'
						}
					})
					.catch((error) => {
						this.loadFlag = false
						this.noticeLoadStatus = 2;
						// this.text = '抱歉，加载列表失败，请稍后重试'
						uni.showModal({
							title: '提示',
							content: error.msg,
							showCancel: false
						})
					})
			},
			go() {
				uni.showModal({
					title: "提示",
					content: '即将上线,敬请期待',
					showCancel: false
				})
			},
			star(type, item) {
				if (!getTicket()) {
					//没登录过在收藏时再加载登录信息去登录
					this.getCollectList()
				}
				console.log('type', type)
				console.log('item', item)
				if (this.showStarFlag) return
				this.showStarFlag = true
				//收藏服务区
				let params = {
					netUserId: getLoginUserInfo().userIdStr,
					collectTitle: item.name,
					// id: item.id,
					collectType: 1
				}
				let url = ''
				if (type == 'star') {
					url = this.$interfaces.addCollect
				} else {
					url = this.$interfaces.delCollect
					params.id = item.starId
				}
				this.$request
					.post(url, {
						data: params
					})
					.then((res) => {
						this.showStarFlag = false
						console.log('收藏结果=====>>>>>', res)
						if (res.code == 200) {
							uni.showToast({
								icon: 'none',
								title: type == 'star' ? '收藏成功，可在我的收藏中查看' : '已取消收藏',
								// title: '已取消收藏',
								duration: 2000
							})
							this.setStarStatus(type, item, res.data.id)
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg,
								// title: '已取消收藏',
								duration: 2000
							})
						}
					})
					.catch((error) => {
						this.showStarFlag = false
						uni.showToast({
							icon: 'none',
							title: error.msg,
							// title: '已取消收藏',
							duration: 2000
						})
					})
			},
			getCollectList() {
				//收藏服务区
				let params = {
					netUserId: getLoginUserInfo().userIdStr,
					collectType: 1
				}
				this.$request
					.post(this.$interfaces.getCollectList, {
						data: params
					})
					.then((res) => {
						console.log('收藏查询=====>>>>>', res)
						if (res.code == 200) {
							let starList = res.data
							if (starList.length > 0) {
								this.netList.forEach((item, index) => {
									starList.forEach((item2) => {
										if (item.name == item2.collectTitle) {
											this.$set(this.netList[index], 'starFlag', true)
											this.$set(this.netList[index], 'starId', item2.id)
										}
									})
								})
							}
						}
					})
					.catch((error) => {})
			},
			setStarStatus(type, listItem, starId) {
				this.netList.forEach((item, index) => {
					if (item.name == listItem.name) {
						this.$set(this.netList[index], 'starFlag', type == 'star' ? true : false)
						this.$set(this.netList[index], 'starId', starId)
						// this.$set(this.netList[index], 'starFlag', false)
					}
				})
			},
			search(name) {
				console.log('name==', name)
				this.pageNo = 1
				this.getNetList(name)
			},
			getLocal() {
				uni.getLocation({
					type: 'wgs84',
					success: (res) => {
						console.log('当前位置的经度：' + res.longitude);
						console.log('当前位置的纬度：' + res.latitude);
						this.longitude = res.longitude
						this.latitude = res.latitude
						this.getNetList()
					},
					fail: (fail) => {
						this.text = '获取不到当前位置信息，无法加载服务区列表'
					}
				});
			},
			//设置服务区条目的显示状态
			setListFlag(list) {
				list.forEach((item, index) => {
					if (item.accessibleBathroom > 0 || item.thirdToilet > 0 || item.easyToilet > 0) {
						this.$set(this.netList[index], 'toiletFlag', true)
					}
					if (item.parkingArea > 0) {
						this.$set(this.netList[index], 'parkFlag', true)
					}
					if (item.tankers > 0) {
						this.$set(this.netList[index], 'tankersFlag', true)
					}
					if (item.chargingPile > 0) {
						this.$set(this.netList[index], 'chargingFlag', true)
					}
					if (item.storeArea > 0) {
						this.$set(this.netList[index], 'storeFlag', true)
					}
					if (item.restaurantCount > 0) {
						this.$set(this.netList[index], 'restaurantFlag', true)
					}
				})
				// console.log('this.netcc===>>>', list, this.netList)
			},
			// calculateDistance(list) {
			// 	let str = ''
			// 	let to = ''
			// 	list.forEach((item, index) => {
			// 		// let changeObj = this.bMapTransQQMap(item.longitude, item.latitude)
			// 		str = str + item.latitude + ',' + item.longitude + ';'
			// 		if ((list.length - 1) == index) {
			// 			// console.log('arr.length', arr)
			// 			to = str.slice(0, -1);
			// 		}
			// 	})
			// 	this.getDistance(to)
			// },
			// getDistance(to) {
			// 	let params = {
			// 		from: this.latitude + ',' + this.longitude,
			// 		to: to,
			// 		key: 'WGUBZ-VR765-6D5IX-IUQ5P-P5KSJ-27FF5'
			// 	}
			// 	console.log('params====>>>>', params)
			// 	uni.request({
			// 		url: 'https://apis.map.qq.com/ws/distance/v1/matrix?mode=driving',
			// 		method: 'POST',
			// 		data: params,
			// 		success: (res) => {
			// 			console.log('计算距离响应res====>>>>', res)
			// 			if (res.data.status == 0) {
			// 				this.setDistance(res.data.result.rows)
			// 			} else {
			// 				// this.text = '抱歉，加载数据失败，请稍后重试'
			// 				this.showDistance = false
			// 			}
			// 		},
			// 		fail: (err) => {
			// 			this.showDistance = false
			// 		}
			// 	})
			// },
			// setDistance(result) {
			// 	console.log('返回的距离信息====>>>>', result)
			// 	this.netList.forEach((item, index) => {
			// 		result[0].elements.forEach((item2, index2) => {
			// 			this.$set(this.netList[index2], 'distance', this.showDistance ? item2.distance :
			// 				'未知')
			// 		})
			// 	})
			// 	console.log('最终距离信息====>>>>', this.netList)
			// },
		}
	};
</script>

<style lang="scss" scoped>
	.high-service {
		font-family: PingFang SC, PingFang SC;

		.right-item {
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 26rpx;
			line-height: 36rpx;
			text-align: left;
			font-style: normal;

			.right-icon {
				width: 32rpx;
				height: 32rpx;
				margin: 0 20rpx 0 16rpx;
			}
		}

		.item-wrapper {
			overflow: hidden;
			height: 840rpx;
			// margin-bottom: 20rpx;
		}

		.tips-text {
			margin-bottom: 20rpx;
			height: 120rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			color: #999999;
		}
	}
</style>