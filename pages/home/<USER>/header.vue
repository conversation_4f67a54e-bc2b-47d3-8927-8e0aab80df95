<template>
	<view class="header">
		<view class="header-wrapper">
			<view class="header-title">
				<view class="bg-image">
					<image src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>"
						class="img" mode="aspectFill"></image>

				</view>
				<view class="title" :style="{'top':marginTop,'height':height,'lineHeight':height}">桂小通(广西捷通)</view>
			</view>
			<view class="header-account g-flex g-flex-align-center" :style="{top:headerAccountTop}">
				<view class="header-account_hd">
					<image :src="getAvatar" class="img" mode="aspectFill"></image>
				</view>
				<view class="header-account_bd">
					<view class="name">
						<block v-if="checkLoginStatus">
							<text v-if="customerInfo.customer_name">
								{{customerInfo.customer_name}}
							</text>
							<text v-else>
								{{loginInfo.loginName | loginNameFilter}}
							</text>
						</block>
						<block v-else>请登录</block>
					</view>
				</view>
				<view class="header-account_ft g-flex g-flex-horizontal-vertical" v-if="!checkLoginStatus">
					<view class="desc">
						<!-- 					<view class="desc" @click="customerChange">
						<block v-if="customerInfo.customer_name">用户切换</block>
						<block v-else>去绑定</block> -->
						<!-- 搜索框和消息通知 -->
						<view class="desc" @click="goLoginHandle">
							请登录
						</view>
					</view>
				</view>
				<view v-if="checkLoginStatus" class="g-flex g-flex-horizontal-vertical">
					<view class="desc g-flex g-flex-center g-flex-align-center">
						<!-- 						<block v-if="customerInfo.customer_name">用户切换</block>
						<block v-else>去绑定</block> -->
						<!-- 搜索框和消息通知 -->
						<view class="search-wrapper" @click="toSearch">
							<!-- <uni-icons class="search-icon" type="search" color="#979797" size="20"></uni-icons> -->
							<view class="search-text">
								搜索
							</view>
							<image class="search-icon" style="width: 33rpx;height: 33rpx"
								src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/search_icon.png"
								mode=""></image>
						</view>
						<!-- 						<uni-icons @click="toNotice" class="message-icon" type="search" color="#979797"
							size="20"></uni-icons> -->
						<image @click="toNotice" class="message-icon"
							src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/msg_icon.png"
							mode=""></image>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	let rpxTopx = function(px) {
		let deviceWidth = uni.getSystemInfoSync().windowWidth; //获取设备屏幕宽度
		let rpx = (750 / deviceWidth) * Number(px)
		return Math.floor(rpx);
	}
	import {
		getTicket,
		getMd5Key,
		getAesKey,
		getLoginUserInfo,
		getCurrUserInfo
	} from '@/common/storageUtil.js'
	export default {
		props: {
			customerInfo: {
				type: Object,
				default () {
					return {}
				}
			}
		},
		data() {
			return {

			}
		},
		components: {

		},
		computed: {
			letnavHeight() {
				const {
					platform,
					statusBarHeight
				} = uni.getSystemInfoSync()
				let _statusBarHeight = statusBarHeight
				let menuButtonObject = uni.getMenuButtonBoundingClientRect();

				let data = statusBarHeight + menuButtonObject.height + (menuButtonObject.top - statusBarHeight) * 2;
				return menuButtonObject.height
			},
			height() {
				return rpxTopx(this.letnavHeight) + 'rpx'
			},
			marginTop() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect();
				return rpxTopx(menuButtonObject.top) + "rpx"
			},
			headerAccountTop() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect();
				let top = rpxTopx(menuButtonObject.bottom) + 40
				return top + "rpx"
			},
			checkLoginStatus() {
				return !!(getTicket() && getMd5Key() && getAesKey() && this.getUserNo)
			},
			getUserNo() {
				return getLoginUserInfo() && Object.keys(getLoginUserInfo()).length ? getLoginUserInfo().userNo : ''
			},
			getAvatar() {
				if (!this.checkLoginStatus) {
					return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';
				}
				if (this.customerInfo && this.customerInfo.customer_type == 0) {
					return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';
				}
				if (this.customerInfo && this.customerInfo.customer_type == 1) {
					return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';
				}
				return 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>';
			},
			loginInfo() {
				return getLoginUserInfo() && Object.keys(getLoginUserInfo()).length ? getLoginUserInfo() : null
			},
		},
		mounted() {
			// -自定义顶部高度 + 胶囊按钮顶部位置+ 用户信息高度+ 用户距离胶囊marginTop+广告marginTop
			let top = -436 + rpxTopx(uni.getMenuButtonBoundingClientRect().bottom) + 66 + 40 + 30
			let style = {
				'marginTop': top + 'rpx',
			}
			this.$emit('on-style', style);

		},
		methods: {
			toSearch() {
				uni.navigateTo({
					url: '/pagesD/home/<USER>/search/index'
				})
			},
			toNotice() {
				uni.navigateTo({
					url: '/pagesD/home/<USER>/msgList/index'
				})
			},
			customerChange() {
				uni.navigateTo({
					url: '/pagesB/accountBusiness/accountList/accountList'
				})
			},
			goLoginHandle() {
				uni.reLaunch({
					url: '/pagesD/login/p-login'
				})
			},
		},
		filters: {
			loginNameFilter(name) {
				const regex = /(\d{3})\d+(\d{4})/;
				let hideCount = (name.toString().length - 7);
				let starString = hideCount > 0 ? '*'.repeat(hideCount) : '';
				return !!name && hideCount > 0 ? name.replace(regex, `$1${starString}$2`) : name
			}
		},
	}
</script>

<style lang="scss">
	.header {
		width: 100%;

		.search-wrapper {
			position: relative;
			width: 220rpx;
			height: 62rpx;
			background: rgba(255, 255, 255, 0.77);
			box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.07);
			border-radius: 37rpx 37rpx 37rpx 37rpx;

			.search-text {
				text-align: center;
				line-height: 62rpx;
				color: #999;
			}

			.search-icon {
				position: absolute;
				right: 16rpx;
				top: 14rpx
			}
		}

		.message-icon {
			width: 40rpx;
			height: 40rpx;
			margin-left: 26rpx;
		}

		.header-wrapper {
			width: 100%;
			position: relative;

			.header-title {
				height: 437rpx;
				position: relative;

				.bg-image {
					width: 100%;
					height: 437rpx;

					.img {
						display: block;
						width: 100%;
						height: 100%;
					}
				}

				.title {
					color: #fff;
					font-size: 32rpx;

					font-weight: 600;
					color: #333333;
					position: absolute;
					width: 100%;
					z-index: 5;
					text-align: center;
					font-weight: bold;
				}
			}

			.header-account {
				width: 100%;
				padding: 0 30rpx;
				position: absolute;
			}

			.header-account .header-account_hd {
				width: 66rpx;
				height: 66rpx;
			}

			.header-account .header-account_hd .img {
				display: block;
				width: 100%;
				height: 100%;
			}

			.header-account .header-account_bd {
				flex: 1;
				margin-left: 14rpx;

			}

			.header-account .header-account_bd .name {
				font-weight: 400;
				color: #323435;
				font-size: 28rpx;
			}

			.header-account .header-account_ft {
				width: 168rpx;
				height: 50rpx;
				background: rgba(199, 199, 199, 0.27);
				border-radius: 24rpx;
				position: relative;
			}

			.header-account .header-account_ft .desc {
				font-size: 28rpx;
				font-weight: 400;
				color: #858686;
				padding-right: 8rpx;
			}

			.header-account .header-account_ft:after {
				content: " ";
				display: inline-block;
				height: 6px;
				width: 6px;
				border-width: 1px 1px 0 0;
				border-color: #858686;
				border-style: solid;
				-webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
				transform: matrix(.71, .71, -.71, .71, 0, 0);
				position: relative;
				top: -2px;
				position: absolute;
				top: 50%;
				margin-top: -4px;
				right: 6px;
			}
		}
	}
</style>