<template>
  <view class="custom-bussiness commonly-used">
    <uni-swiper-dot
      class="swiper-dotc"
      :info="sortArr.length > 0 ? info : []"
      :current="current"
      :mode="'default'"
      :dotsStyles="dotsStyles"
    >
      <swiper class="swiper" @change="change" :current="current">
        <swiper-item>
          <view class="commonly-list">
            <block v-for="(el, idx) in originalArr" :key="idx">
              <view
                class="card"
                :id="`card-${idx}`"
                v-if="!el.isNoVisible"
                @click="onMenuHandle(el)"
                @longtap.stop="longTap(el)"
                @touchend="touchend"
              >
                <image
                  :src="el.icon"
                  class="itemImg"
                  mode="monthlyBillt"
                ></image>
                <view class="name">{{ el.name }}</view>
              </view>
            </block>
          </view>
        </swiper-item>
        <swiper-item v-if="sortArr.length > 0">
          <view class="commonly-list">
            <block v-for="(el, idx) in sortArr" :key="idx">
              <view
                class="card"
                :id="`card-${idx}`"
                v-if="!el.isNoVisible"
                @click="onMenuHandle(el)"
                @longtap.stop="longTap(el)"
                @touchend="touchend"
              >
                <image
                  :src="el.icon"
                  class="itemImg"
                  mode="monthlyBillt"
                ></image>
                <view class="name">{{ el.name }}</view>
                <!-- 浮层 -->
                <view class="popups dark top-center" v-if="el.shoWrap">
                  <view class="mask mask-show" @tap.stop="tapMask(el)"></view>
                  <!-- <text class="triangle bottom-center" /> -->
                  <view class="popups-row" @click.stop="cancelCollect(el)">
                    取消收藏
                  </view>
                </view>
              </view>
            </block>
          </view>
        </swiper-item>
      </swiper>
    </uni-swiper-dot>
  </view>
</template>

<script>
import {
  getTicket,
  getMd5Key,
  getAesKey,
  getLoginUserInfo,
  getCurrUserInfo
} from "@/common/storageUtil.js";
import { skipControlHandle } from "@/components/home/<USER>";
import mapAPI from "@/common/api/map.js";
import { menuConfig } from "@/components/home/<USER>";

export default {
  data() {
    return {
      islongPress: false,
      originalArr: [
        {
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/new-apply.png",
          name: "ETC新办",
          type: "newApply",
          url: "/pagesA/newBusiness/productSelect/productSelect",
          validateRule: "login"
        },
        // {
        //   icon:
        //     "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/truck-platform.png",
        //   name: "货运平台",
        //   type: "platform",
        //   // url: "/pagesB/invoiceBusiness/home/<USER>",
        //   validateRule: "login"
        // },
        {
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/load_iconv1.0.1.png",
          name: "ETC充值",
          type: "recharge",
          // url: "/pagesB/invoiceBusiness/home/<USER>",
          validateRule: "login"
        },
        {
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/invoice_icon.png",
          name: "开票服务",
          type: "invoice",
          url: "/pagesB/invoiceBusiness/home/<USER>",
          validateRule: "etcAccount"
        },
        {
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/jiuyuan.png",
          name: "一键救援",
          type: "rescue",
          url: '/pagesD/travelService/rescue/index',
          validateRule: "login"
        },
        {
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/ecss-syncv1.0.1.png",
          name: "充电服务",
          type: "jtCharge",
          validateRule: "login",
          url: '/pagesD/charging/index',
          isNoVisible: false,
          extensionType: ""
        },
        {
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/home/<USER>",
          name: "设备售后",
          type: "afterSale",
          url: "/pagesC/afterSaleBusiness/vehicleList/index",
          validateRule: "login",
          extensionType: "",
          isNoVisible: false //线上发行上线先屏蔽入口
        },
        // {
        //   icon: "../../../static/toc/icon_selfcheck.png",
        //   name: "设备检测",
        //   type: "selfCheck",
        //   url: "/pagesC/selfCheck/index",
        //   validateRule: "login",
        //   extensionType: ""
        // },
        {
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/unit-car.png",
          name: "消费记录",
          type: "consumptionRecord",
          url: "/pagesB/vehicleBusiness/vehicleList?fontType=consumptionRecord",
          validateRule: "etcAccount",
          extensionType: ""
        },
        // {
        //   icon:
        //     "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/home/<USER>",
        //   name: "服务订单",
        //   type: "serviceOrder",
        //   url: "/pagesA/serviceOrder/index",
        //   validateRule: "login",
        //   extensionType: ""
        // },
        {
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/all-icon.png",
          name: "全部业务",
          type: "selfCheck",
          url: "/pagesD/home/<USER>/index",
          validateRule: "login"
        }
      ],
      sortArr: [],
      allItem: {
        icon:
          "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/all-icon.png",
        name: "全部业务",
        type: "selfCheck",
        url: "/pagesD/home/<USER>/index",
        validateRule: "login",
        extensionType: ""
      },
      current: 0,
      info: [1, 2],
      dotsStyles: {
        bottom: 3,
        selectedBackgroundColor: "#0066E9"
      }
    };
  },
  computed: {
    checkLoginStatus() {
      return !!(getTicket() && getMd5Key() && getAesKey() && this.getUserNo);
    },
    getUserNo() {
      return getLoginUserInfo() && Object.keys(getLoginUserInfo()).length
        ? getLoginUserInfo().userNo
        : "";
    },
    currentCustomer() {
      return this.checkLoginStatus ? getCurrUserInfo() : {};
    }
  },
  methods: {
    change(e) {
      console.log(e);
      this.current = e.detail.current;
    },
    customerChange() {
      uni.navigateTo({
        url: "/pagesB/accountBusiness/accountList/accountList"
      });
    },
    goLoginHandle() {
      uni.reLaunch({
        url: "/pagesD/login/p-login"
      });
    },
    onMenuSkipHandle(item) {
      if (item.extensionType && item.extensionType == "webview") {
        this.goWebviewHandle(item);
        return;
      }
      this.goPagesHandle(item);
    },
    // 验证登录
    validateLogin() {
      if (!this.checkLoginStatus) {
        uni.showModal({
          title: "提示",
          content: "请先登录",
          success: res => {
            if (res.confirm) {
              uni.reLaunch({
                url: "/pagesD/login/p-login"
              });
            }
          }
        });
        return;
      }
      return true;
    },
    // 验证绑定ETC用户
    validateBindAccount() {
      if (!(getCurrUserInfo() && getCurrUserInfo().customer_id)) {
        uni.showModal({
          title: "提示",
          content: "请先绑定ETC用户",
          success: function(res) {
            if (res.confirm) {
              uni.navigateTo({
                url: "/pagesB/accountBusiness/accountList/accountList"
              });
            }
          }
        });
        return;
      }
      return true;
    },
    // webview 跳转模式
    goWebviewHandle(item) {
      // 在线客服
      if (item.type == "onlineserver") {
        var callcenter =
          "https://ccrm.wengine.cn/chatui/#/app/online?tntInstId=HSYQGXGS&scene=SCE0000027";
        uni.navigateTo({
          url:
            "/pages/uni-webview/uni-webview?ownPath=" +
            encodeURIComponent(callcenter)
        });
      }
    },
    toMiniProgram(type) {
      if (type == "platform") {
        uni.navigateToMiniProgram({
          appId: "wx1b6a17b21753c694",
          path: "/pages/index/auth",
          envVersion: "trial", //正式版
          // extraData: params,
          success(res) {}
        });
      } else if (type == "rescue1") {
        // uni.navigateToMiniProgram({
        //   appId: "wx1b6a17b21753c694",
        //   path: "/pages/index/auth",
        //   envVersion: "trial", //正式版
        //   // extraData: params,
        //   success(res) {}
        // });
        uni.showModal({
          title: "提示",
          content: "一键救援服务即将上线，如需服务请拨打服务热线96333",
          success: res => {
            if (res.confirm) {
              uni.makePhoneCall({
                phoneNumber: "0771-96333" //仅为示例
              });
            }
          }
        });
      }
    },
    // 小程序页面 跳转模式
    goPagesHandle(item) {
      // 根据菜单配置页面路由地址
      if (item.url) {
        uni.navigateTo({
          url: item.url
        });
        return;
      }
      // 特殊处理路由页面
      const specialType = ["afterPay"];

      if (item.type == "recharge") {
        let flag = !!this.sortArr.length;
        let url = flag
          ? "/pagesB/rechargeBusiness/selectVehicle/index"
          : "/pagesB/rechargeBusiness/selectVehicle/index";
        uni.navigateTo({
          url: url + "?fontType=" + item.type
        });
        return;
      }
      if (specialType.includes(item.type) && !this.sortArr.length) {
        uni.navigateTo({
          url: "/pagesB/vehicleBusiness/noVehicleList?fontType=" + item.type
        });
        return;
      }

      uni.navigateTo({
        url: "/pagesB/vehicleBusiness/sortArr?fontType=" + item.type
      });
    },
    onMenuHandle(item) {
      if (this.islongPress) return;
      // 特勤业务 如车生活
      let type = item.type;
      let flag = skipControlHandle(item.type);
      if (!flag) return;

      if (type == "platform" || type == "rescue1" ) {
        this.toMiniProgram(type);
        return;
      }

      // 游客模式 无需验证登录
      if (item.validateRule == "visitor") {
        this.onMenuSkipHandle(item);
        return;
      }
      // 登录模式 无需绑定ETC用户
      if (item.validateRule == "login") {
        if (!this.validateLogin()) return;
        this.onMenuSkipHandle(item);
        return;
      }
      // ETC用户模式 需绑定ETC用户
      if (item.validateRule == "etcAccount") {
        if (!this.validateLogin()) return;
        if (!this.validateBindAccount()) return;
        this.onMenuSkipHandle(item);
        return;
      }
    },
    longTap(el) {
      console.log(el);
      if (!el.id) return;
      this.islongPress = true;
      this.$set(el, "shoWrap", true);
    },
    tapPopup(e) {
      console.log(e);
    },
    touchend() {
      //延时执行为了防止 click() 还未判断 islongPress 的值就被置为 fasle
      setTimeout(() => {
        this.islongPress = false;
      }, 200);
    },
    async cancelCollect(el) {
      let params = {
        id: el.id,
        netUserId: getLoginUserInfo().userIdStr,
        menuId: el.menuId
      };
      let res = await this.$request.post(mapAPI.deleteMenu, {
        data: params
      });
      if (res.code == 200) {
        this.tapMask(el);
        uni.showToast({
          title: "取消成功",
          duration: 1000
        });
        this.getMenu();
      }
    },
    tapMask(el) {
      this.sortArr.forEach(item => {
        this.$set(item, "shoWrap", false);
      });
      console.log(el, 1212112121212);
    },
    async getMenu() {
      if(!getLoginUserInfo().userIdStr) return
      let params = {
        netUserId: getLoginUserInfo().userIdStr
      };
      let res = await this.$request.post(mapAPI.userMenuList, {
        data: params
      });
      if (res.code == 200 && res.data) {
        this.sortMenu(res.data);
      }
    },
    // 根据menuId匹配应用
    sortMenu(arr) {
      let menuList = [];
      let sortArr = [];
      menuConfig.forEach(item => {
        menuList.push(...item.sortArr);
      });
      menuList.forEach(item => {
        arr.forEach(el => {
          if (el.menuId == item.menuId) {
            sortArr.push({
              id: el.id,
              ...item
            });
          }
        });
      });
      this.sortArr = sortArr;
      console.log("aesKey222222222===>> ", this.sortArr, 2222222222);
    }
  },
  created() {
    // this.getMenu();
  }
};
</script>

<style lang="scss" scoped>
.custom-bussiness {
  background: #ffffff;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  margin: 20rpx;
  .swiper {
    width: 710rpx;
    height: 328rpx;
    padding: 19rpx;
    box-sizing: border-box;
  }
  /** mask：遮罩 */
  .mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 8888;
    visibility: hidden;
    transition: background 0.3s ease-in-out;

    &.mask-show {
      visibility: visible;
    }
  }

  /** menu：菜单弹窗 */
  .popups {
    position: absolute;
    display: flex;
    align-items: center;
    // height: 45rpx;
    width: 100%;
    height: 100%;
    border-radius: 10px;
  }
  .popups-row {
    width: 100%;
    text-align: center;
    font-size: 24rpx;
    padding: 20rpx 0;
    z-index: 9999;
  }

  .triangle {
    width: 0px;
    height: 0px;
  }

  .dark {
    background-color: rgba(12, 10, 10, 0.7);
    color: #fff;
    top: 0;
    left: 0;
    border-radius: 16rpx;
    font-weight: 400;
    .top-center:after {
      content: "";
      position: absolute;
      left: 47%;
      border-style: solid;
      border-color: transparent transparent #4c4c4c;
    }
  }
}

.commonly-used {
  display: flex;
  // flex-direction: column;
  .commonly-list {
    display: flex;
    flex-wrap: wrap;

    .card {
      display: flex;
      width: 25%;
      flex-direction: column;
      align-items: center;
      position: relative;
      // margin-right: 29rpx;
      margin-bottom: 20rpx;
      &:last-child,
      &:nth-child(4) {
        margin-right: 0;
      }
      .itemImg {
        margin-bottom: 14rpx;
        width: 88rpx;
        height: 88rpx;
      }
      .name {
        font-weight: 400;
        font-size: 26rpx;
        color: #333333;
        text-align: center;
      }
    }
  }
}
</style>