<template>
  <view class="road-info">
    <view class="info-title">路况信息</view>
    <MapWrapCom
      :polyline="polyline"
      :scale="scale"
      :markers="markers"
      :mapLocation="mapLocation"
      enableTraffic
      ref="MapWrapCom"
      class="MapWrapCom"
      height="380rpx"
      :isCenter="true"
      @updateLocation="initMapInfo"
      @onmarkertap="onmarkertap"
      @onpolylinetap="onpolylinetap"
    />
    <!-- 地图工具 -->
    <mapTool @toolTap="toolTap" class="controls-wrap" />
    <!-- 底部信息 -->
    <!-- <bottomInfo
      class="info"
      :infoType="markerInfoType"
      v-show="showType == 'info'"
      @closeInfo="closeInfo"
    /> -->
    <swiperBtmInfo
      ref="swiperBtmInfo"
      class="info"
      :infoType="markerInfoType"
      v-show="showType == 'swiper'"
      @closeInfo="closeInfo"
	  @onmarkerchange="onmarkerchange"
      :bannerList="bannerList"
    />
  </view>
</template>

<script>
import MapWrapCom from "@/components/map/map.vue";
import mapTool from "@/components/map/map-tool.vue";
// import bottomInfo from "@/components/map/bottom-info-view.vue";
import swiperBtmInfo from "@/components/map/swiper-btmInfo.vue";
let markersTemp = {
  zIndex: "1",
  rotate: 0,
  width: 30,
  height: 30,
  anchor: {
    x: 0.5,
    y: 1
  }
};
export default {
  components: {
    MapWrapCom,
    mapTool,
    // bottomInfo,
    swiperBtmInfo
  },
  data() {
    return {
      scale: 15,
      bannerList: [
        // { infoType: "work" },
        // { infoType: "jam" },
        // { infoType: "event" },
        // { infoType: "road" }
      ],
      includePoints: [],
      markers: [],
      polyline: [],
      dataMarkers: [],
      showType: "swiper", // swiper - 轮播 info - 信息
      // showType: "marker", // polyline - 路线导航 ，marker - 点信息
      markerInfoType: "event",
      roadData: null
    };
  },
  methods: {
    addMarkers(type) {
      let markers = this.dataMarkers.filter(item => item.type == type);
      this.markers.push(...markers);
      // this.includePoints.push(...markers);
      // console.log(this.includePoints, "includePoints");
    },
    deleteMarkers(type) {
      this.markers = this.markers.filter(item => item.type != type);
      // this.includePoints = this.includePoints.filter(item => item.type != type);
    },
    toolTap(item) {
      // console.log(item);
      if (item.type == "road") {
        if (item.checkd) {
          this.addPolyline();
        } else {
          this.clearPolyline(item.type);
        }
      } else {
        if (item.checkd) {
          this.addMarkers(item.type);
        } else {
          this.deleteMarkers(item.type);
        }
      }
    },
    addPolyline(road) {
      let data = road || this.roadData;
      let lineArr = data.map((item, idx) => {
        var coors = JSON.parse(item.polyline);
        let pl = [];
        // //坐标解压（返回的点串坐标，通过前向差分进行压缩）
        // var kr = 10000000;
        // for (var i = 2; i < coors.length; i++) {
        //   coors[i] = Number(coors[i - 2]) + Number(coors[i]) / kr;
        // }
        //将解压后的坐标放入点串数组pl中
        for (var i = 0; i < coors.length; i += 2) {
          pl.push({ latitude: coors[i], longitude: coors[i + 1] });
        }
        let polyline = {
          id: item.id,
          points: pl,
          type: "road",
          color: idx == 0 ? "#FDD925" : "#58c16c",
          width: 3,
          borderColor: "#2f693c",
          borderWidth: 1
        };
        // console.log(pl, "plpl");
        let distance =
          item.distance >= 1000000
            ? (item.distance / 1000).toFixed(0)
            : (item.distance / 1000).toFixed(1);
        // 将分钟转换为小时和分钟
        const hours = Math.floor(item.duration / 60); // 取整
        const remainingMinutes = item.duration % 60; // 余数
        let duration = `${
          hours >= 1 ? hours + "小时" : ""
        }${remainingMinutes}分钟`;
        return {
          lineId: idx,
          polyline,
          distance,
          duration
        };
      });
      lineArr.forEach(item => {
        // 绘制路线
        // console.log(item, 222);
        setTimeout(() => {
          this.polyline.push(item.polyline);
        });
      });
    },
    clearPolyline() {
      this.polyline = [];
    },
	//事件绑定marker放大
	onmarkerchange(e){
		this.markers.forEach(item => {
			if (item.id == e.id) {
			  // console.log(e.target.markerId, item.id);
			  item.width = 60;
			  item.height = 60;
			  // this.markerInfoType = e.infoType;
			  // this.$refs.swiperBtmInfo.pickInfo(item);
			  // this.showType = "swiper";
			} else {
			  item.width = 30;
			  item.height = 30;
			}
		});
	},
    onmarkertap(e) {
      // console.log(e.target);
      this.markers.forEach(item => {
        if (item.id == e.target.markerId) {
          // console.log(e.target.markerId, item.id);
          item.width = 60;
          item.height = 60;
          this.markerInfoType = item.type;
          this.$refs.swiperBtmInfo.pickInfo(item);
          this.showType = "swiper";
        } else {
          item.width = 30;
          item.height = 30;
        }
      });
    },
    closeInfo() {
      this.showType = "";
    },
    onpolylinetap(e) {
      this.roadData.forEach(item => {
        if (item.id == e.target.polylineId) {
          // console.log(e.target.polylineId, item.id);
          this.markerInfoType = item.type;
          let ids = this.bannerList.map(item => item.id);
          if (!ids.includes(item.id)) {
            item.infoType = "road";
            this.bannerList.push(item);
          }
          setTimeout(() => {
            this.$refs.swiperBtmInfo.pickInfo(item);
          });
          this.selectLineLight(e);
          this.showType = "swiper";
        }
      });
    },
    selectLineLight(e) {
      this.polyline.forEach(item => {
        if (item.id == e.target.polylineId && item.type == "road") {
          // console.log(item);
          item.color = "#4F90FF";
        } else {
          if (!item.type) return;
          item.color = item.lately ? "#FDD925" : "#58c16c";
        }
      });
    },
    // 获取优惠路段
    getRoad(location) {
      let params = {
        latitude: location.latitude,
        longitude: location.longitude
        // pageNo: 1,
        // pageSize: 6
      };
      this.$request
        .post(this.$interfaces.getMapRoad, {
          data: params
        })
        .then(res => {
          let { data } = res;
          this.roadData = data;
          // console.log(this.roadData, "roadData");
		  if(this.roadData.length){
			  this.addPolyline(data);
			  let swiper = data.map(item => {
				return {
				  infoType: "road",
				  ...item
				};
			  });
			  this.bannerList.push(swiper[0]);
		    }
        });
    },
    // 获取事件
    getEvent(location) {
      let params = {
        latitude: location.latitude,
        longitude: location.longitude,
        pageNo: 1,
        pageSize: 2
      };
      this.$request
        .post(this.$interfaces.getMapEvent, {
          data: params
        })
        .then(res => {
          // console.log(res, "event");
          let { data } = res;
          let markers = data.map(item => {
            return {
              ...markersTemp,
              id: item.id,
              latitude: item.lat,
              longitude: item.lon,
              title: item.loadName,
              type: "event",
              iconPath:
                "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/event_light.png"
            };
          });
          this.dataMarkers.push(...markers);
          this.markers.push(...markers);
          let swiper = data.map(item => {
            return {
              infoType: "event",
              ...item
            };
          });
          this.bannerList.push(...swiper);
        });
    },
    // 获取拥堵信息
    getJamInfo(location) {
      let params = {
        latitude: location.latitude,
        longitude: location.longitude,
        pageNo: 1,
        pageSize: 2
      };
      this.$request
        .post(this.$interfaces.getMapJam, {
          data: params
        })
        .then(res => {
          // console.log(res, "Jam");
          let { data } = res;
          let markers = data.map(item => {
            return {
              ...markersTemp,
              id: item.id,
              latitude: item.lat,
              longitude: item.lng,
              title: item.congestSourceArea,
              type: "jam",
              iconPath:
                "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/jam_light.png"
            };
          });
          this.dataMarkers.push(...markers);
          this.markers.push(...markers);
          let swiper = data.map(item => {
            return {
              infoType: "jam",
              ...item
            };
          });
          this.bannerList.push(...swiper);
        });
    },
    // 获取施工
    getWork(location) {
      let params = {
        latitude: location.latitude,
        longitude: location.longitude,
        pageNo: 1,
        pageSize: 2
      };
      this.$request
        .post(this.$interfaces.getMapWork, {
          data: params
        })
        .then(res => {
          // console.log(res, "Work");
          let { data } = res;
          let markers = data.map(item => {
            return {
              ...markersTemp,
              id: item.id,
              latitude: item.startLatitude,
              longitude: item.startLongitude,
              title: item.highspeedName,
              type: "work",
              iconPath:
                "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/sg_light.png"
            };
          });
          this.dataMarkers.push(...markers);
          this.markers.push(...markers);
          let swiper = data.map(item => {
            return {
              infoType: "work",
              ...item
            };
          });
          this.bannerList.push(...swiper);
          // console.log(this.bannerList, "bannerList");
        });
    },
    initMapInfo(location) {
      // this.getRoad(location);
      this.getEvent(location);
      this.getJamInfo(location);
      this.getWork(location);
    }
  },
  created() {
    // this.markers = this.dataMarkers;
    // this.getRoad();
  }
};
</script>

<style lang="scss" scoped>
.road-info {
  margin: 24rpx 20rpx;
  padding: 20rpx;
  // padding-bottom: 0;
  background: #fff;
  position: relative;
  // height: 928rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  .MapWrapCom {
    border-radius: 8rpx 8rpx 8rpx 8rpx;
  }
  .info-title {
    font-weight: 600;
    font-size: 30rpx;
    color: #333333;
    margin-bottom: 20rpx;
  }
  .controls-wrap {
    position: absolute;
    right: 44rpx;
    top: 108rpx;
  }
  .info {
    width: 100%;
    // position: absolute;
    // left: 0;
    // bottom: 26rpx;
  }
  /deep/ .info-wrap {
    padding-top: 4rpx;
  }
}
</style>