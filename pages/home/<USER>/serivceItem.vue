<template>
  <view class="service-item" @click="goDetail(item)">
    <image
      v-if="count"
      class="big-img"
      :src="imageUrl"
      mode=""
	  @error="imageError"
    >
    </image>
    <view class="content-wrapper">
      <view class="middle-wrapper">
        <view class="title-wrapper">
          <view class="title">
            {{ item.name }}
          </view>
          <image
            v-if="!item.starFlag"
            @click.stop="star('star', item)"
            class="star-icon"
            src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/star_icon.png"
            mode=""
          ></image>
          <image
            v-else
            @click.stop="star('noStar', item)"
            class="star-icon"
            src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/star_icon_highlight.png"
            mode=""
          >
          </image>
        </view>
        <view class="desc-wrapper">
          <view class="left">
            {{ item.level }}
          </view>
          <!-- 	<view class="right" v-if="item.distance < 1000">
						距您{{item.distance}}m
					</view>
					<view class="right" v-if="item.distance > 1000">
						距您{{parseInt(item.distance / 1000)}}km
					</view>
					<view class="right" v-if="item.distance == '未知'">
						{{item.distance}}
					</view> -->
          <view class="right"> 距您{{ item.remark | kmFilter }}km </view>
        </view>
      </view>
      <view class="list-wrapper">
        <view
          class="list"
          v-for="(listItem, index) in list"
          :key="index"
          @click.stop="toCharge(item, listItem.key)"
        >
          <image
            :src="item[listItem.key] ? listItem.icon1 : listItem.icon"
            mode=""
          ></image>
          <view class="label">
            {{ listItem.name }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    item: {
      type: Object
    },
    index: {
      type: Number
    }
  },
  components: {},
  computed: {
    count() {
      let count = "";
      if (this.index < 8) {
        count = this.index;
      } else {
        count = this.index - (Math.ceil(this.index / 7) - 1) * 7;
      }
      return count;
    }
  },
  data() {
    return {
      list: [
        {
          name: "卫生间",
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/toilet_icon.png",
          icon1:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/toilet_icon_highlight.png",
          key: "toiletFlag"
        },
        {
          name: "停车场",
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/park_icon.png",
          icon1:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/park_icon_highlight.png",
          key: "parkFlag"
        },
        {
          name: "加油站",
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/tankers_icon.png",
          icon1:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/tankers_icon_highlight.png",
          key: "tankersFlag"
        },
        {
          name: "充电桩",
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/charging_icon.png",
          icon1:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/charging_icon_highlight.png",
          key: "chargingFlag"
        },
        {
          name: "便利店",
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/store_icon.png",
          icon1:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/store_icon_highlight.png",
          key: "storeFlag"
        },
        {
          name: "餐厅",
          icon:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/restaurant_icon.png",
          icon1:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/restaurant_icon_highlight.png",
          key: "restaurantFlag"
        }
      ],
	  picPre:'https://portal.gxetc.com.cn/service-area-pic',
	  imageUrl:''
    };
  },
  created() {
	  this.imageUrl = this.item.mainPic && this.item.mainPic.length > 0 ? this.picPre + '' + this.item.mainPic.replace(/\\/g,'/') :
		'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ' + this.count + '.png'
  },
  methods: {
	imageError(e){
		this.imageUrl = 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ' +
          this.count + '.png'
	},
    toCharge(item, listItemKey) {
      if (listItemKey == "chargingFlag" && item[listItemKey]) {
        uni.navigateToMiniProgram({
          appId: "wx33901e6f35bd973c",
          // path: '/pages/index/auth',
          // envVersion: 'trial', //正式版
          // extraData: params,
          success(res) {}
        });
      }
    },
    star(type, item) {
      this.$emit("star", type, item);
    },
    goDetail(row) {
      console.log(111);
      uni.navigateTo({
        url: `/pagesD/travelService/serviceArea/detail?id=${row.id}&name=${row.name}&remark=${row.remark}&img=https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ${this.count}.png`
      });
    }
  },
  filters: {
    kmFilter(val) {
      return parseFloat(val).toFixed(1);
    }
  }
};
</script>

<style lang="scss" scoped>
.service-item {
  position: relative;
  display: flex;
  // align-items: center;
  font-family: PingFang SC, PingFang SC;
  // height: 205rpx;
  margin: 20rpx 20rpx 38rpx 20rpx;
  border-bottom: 2rpx solid #e7e7e7;
  &:last-child {
    margin-bottom: 20rpx;
  }

  .big-img {
    margin-bottom: 32rpx;
    width: 248rpx;
    height: 210rpx;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
  }

  .content-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 20rpx;
    flex: 1;
    margin-bottom: 12rpx;

    .title-wrapper {
      margin-bottom: 10rpx;
      display: flex;
      align-items: center;

      .title {
        margin-right: 20rpx;
        font-weight: 600;
        font-size: 30rpx;
        color: #333333;
        line-height: 40rpx;
      }

      .star-icon {
        width: 28rpx;
        height: 27rpx;
      }
    }

    .desc-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 34rpx;
    }

    .list-wrapper {
      display: flex;
      flex-wrap: wrap;
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      line-height: 34rpx;

      .list {
        display: flex;
        align-items: center;
        width: 26%;
        margin-right: 40rpx;
        margin-bottom: 20rpx;

        & > image {
          margin-right: 4rpx;
          width: 32rpx;
          height: 32rpx;
        }

        .label {
          width: 84rpx;
        }

        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
  }
}
</style>