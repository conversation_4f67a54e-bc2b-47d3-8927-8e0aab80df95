<template>
	<view class="high-search">
		<view class="search-wrapper">
			<input class="search-input" type="text" v-model="searchValue" :placeholder="placeholder" />
			<!-- <uni-icons @click="search" class="search-icon" type="search" color="#979797" size="20"></uni-icons> -->
			<image @click="search" class="search-icon"
				src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/search_icon.png" mode="">
			</image>
		</view>
		<slot></slot>
	</view>
</template>
<script>
	export default {
		props: {
			placeholder: {
				type: String,
				default: '请输入'
			}
		},
		components: {},
		data() {
			return {
				searchValue: ''
			};
		},
		methods: {
			search() {
				console.log('this.searchValue', this.searchValue)
				this.$emit('click', this.searchValue)
			}
		}
	};
</script>

<style lang="scss" scoped>
	.high-search {
		display: flex;
		align-items: center;

		.search-wrapper {
			position: relative;
			flex: 1;
			display: flex;
			margin: 20rpx;
			padding: 0 10rpx 0 20rpx;
			height: 62rpx;
			background: #FFFFFF;
			border-radius: 37rpx 37rpx 37rpx 37rpx;
			border: 1rpx solid #E7E7E7;
			font-family: PingFang SC, PingFang SC;

			.search-input {
				width: 100%;
				height: 100%;
				flex: 1;
				line-height: 62rpx;
			}

			.search-icon {
				width: 33rpx;
				height: 33rpx;
				position: absolute;
				right: 16rpx;
				top: 12rpx;
				z-index: 10
			}
		}
	}
</style>