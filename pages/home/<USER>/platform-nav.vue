<template>
  <view class="nav-wrap">
    <view class="platform nav-box" @click="goPlatform">
      <view class="left">
        <view class="title">高速资讯</view>
        <view class="tip">畅行八桂大地</view>
        <view class="btn">进入专区</view>
      </view>
      <view class="right">
        <image
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/platform.png"
          class="home-img"
        ></image>
      </view>
    </view>
    <view class="policy nav-box" @click="goPolicy">
      <view class="left">
        <view class="title">政策宣贯</view>
<!--        <view class="tip">高速收费概览 </view>
        <view class="tip">农产品目录</view> -->
        <view class="btn">进入专区</view>
      </view>
      <view class="right">
        <image
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/policy.png"
          class="home-img"
        ></image>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    goPlatform() {
      // uni.showModal({
      //   title: "提示",
      //   content: "即将上线,敬请期待",
      //   showCancel: false
      // });
      let url = "https://wxweb.gxetc.com.cn/gszx";
      uni.navigateTo({
        url: "/pages/uni-webview/uni-webview?ownPath=" + encodeURIComponent(url)
      });
      // uni.navigateToMiniProgram({
      //   appId: "wx1b6a17b21753c694",
      //   path: "/pages/index/auth",
      //   envVersion: "trial", //正式版
      //   extraData: {},
      //   success(res) {},
      //   fail(err){
      //     console.log(err);
      //   }
      // });
    },
    goPolicy() {
      uni.navigateTo({
        url: "/pagesD/travelService/policy/index"
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.nav-wrap {
  margin: 20rpx;
  display: flex;
  .nav-box {
    margin-right: 22rpx;
    width: 344rpx;
    height: 198rpx;
    border-radius: 10rpx 10rpx 10rpx 10rpx;
    display: flex;
    padding: 16rpx 0 16rpx 24rpx;
    color: #ffffff;
    justify-content: space-between;
    .right {
      width: 144rpx;
      height: 166rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .title {
      font-weight: 500;
      font-size: 28rpx;
    }
    .tip {
      font-weight: 400;
      font-size: 24rpx;
    }
    .btn {
      width: 100rpx;
      height: 40rpx;
      background: #ffffff;
      border-radius: 4rpx 4rpx 4rpx 4rpx;
      font-size: 20rpx;
      text-align: center;
      line-height: 40rpx;
    }
    &.platform {
      background: #4f90ff;
      .title {
        margin-top: 18rpx;
      }
      .btn {
        color: #4f90ff;
        margin-top: 32rpx;
      }
    }
    &.policy {
      background: #f69754;
	  .title {
	    margin-top: 18rpx;
	  }
      .btn {
        color: #f69754;
        // margin-top: 16rpx;
	    margin-top: 62rpx;
      }
    }
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>