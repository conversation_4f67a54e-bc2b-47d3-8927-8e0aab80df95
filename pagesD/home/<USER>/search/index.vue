<template>
  <view class="search-apply">
    <view class="search-wrap">
      <view class="search-box" :class="isSearch ? 'isSearch' : ''">
        <view class="icon">
          <uni-icons type="search" color="#979797" size="20"></uni-icons>
        </view>
        <input
          class="uni-input"
          v-model="applyName"
          @input="filterApply"
          focus
          placeholder="办业务，搜一搜"
        />
      </view>
      <view class="btn" @click="cancelSearch">取消</view>
    </view>
    <view class="content-wrap">
      <view class="warp-title">{{
        sortArr.length > 0 ? "为您找到以下应用" : "搜索历史"
      }}</view>
      <view class="item-list" v-if="sortArr.length == 0">
        <view
          class="info-item"
          v-for="(history, index) in searhHistory.slice(0, 3)"
          @click="onMenuHandle(history)"
          :key="index"
          >{{ history.name }}
        </view>
      </view>
    </view>
    <view class="commonly-list">
      <block v-for="(el, idx) in sortArr" :key="idx">
        <view class="card" v-if="!el.isNoVisible" @click="onMenuHandle(el)">
          <image
            :src="el.icon"
            style="width: 86rpx;height: 86rpx;"
            mode="monthlyBillt"
          ></image>
          <view class="name">{{ el.name }}</view>
        </view>
      </block>
    </view>
  </view>
</template>

<script>
import {
  getTicket,
  getMd5Key,
  getAesKey,
  getLoginUserInfo,
  getCurrUserInfo
} from "@/common/storageUtil.js";
import { menuConfig } from "@/components/home/<USER>";
import { skipControlHandle } from "@/components/home/<USER>";

export default {
  props: {
    vehicleList: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      applyName: "",
      applyList: [],
      sortArr: [],
      searhHistory: []
    };
  },
  computed: {
    isSearch() {
      return this.applyName.length > 0;
    },
    checkLoginStatus() {
      return !!(getTicket() && getMd5Key() && getAesKey() && this.getUserNo);
    },
    getUserNo() {
      return getLoginUserInfo() && Object.keys(getLoginUserInfo()).length
        ? getLoginUserInfo().userNo
        : "";
    },
    currentCustomer() {
      return this.checkLoginStatus ? getCurrUserInfo() : {};
    }
  },
  methods: {
    initList() {
      // 初始化业务列表
      let applyList = [];
      menuConfig.forEach(item => {
        applyList.push(...item.sortArr);
      });
      this.applyList = applyList;

      //初始化历史记录列表
      this.searhHistory = uni.getStorageSync("searhHistory") || [];
    },
    filterApply(e) {
      let val = e.target.value;
      if (!val) {
        this.sortArr = [];
        return;
      }
      this.sortArr = this.applyList.filter(item => {
        return item.name.includes(val);
      });
    },
    cancelSearch() {
      this.applyName = "";
      this.sortArr = [];
      uni.navigateBack();
    },
    onMenuSkipHandle(item) {
      if (item.extensionType && item.extensionType == "webview") {
        this.goWebviewHandle(item);
        return;
      }
      this.goPagesHandle(item);
    },
    // 验证登录
    validateLogin() {
      if (!this.checkLoginStatus) {
        uni.showModal({
          title: "提示",
          content: "请先登录",
          success: res => {
            if (res.confirm) {
              uni.reLaunch({
                url: "/pagesD/login/p-login"
              });
            }
          }
        });
        return;
      }
      return true;
    },
    // 验证绑定ETC用户
    validateBindAccount() {
      if (!(getCurrUserInfo() && getCurrUserInfo().customer_id)) {
        uni.showModal({
          title: "提示",
          content: "请先绑定ETC用户",
          success: function(res) {
            if (res.confirm) {
              uni.navigateTo({
                url: "/pagesB/accountBusiness/accountList/accountList"
              });
            }
          }
        });
        return;
      }
      return true;
    },
    // webview 跳转模式
    goWebviewHandle(item) {
      // 在线客服
      if (item.type == "onlineserver") {
        var callcenter =
          "https://ccrm.wengine.cn/chatui/#/app/online?tntInstId=HSYQGXGS&scene=SCE0000027";
        uni.navigateTo({
          url:
            "/pages/uni-webview/uni-webview?ownPath=" +
            encodeURIComponent(callcenter)
        });
      }
    },
    toMiniProgram(type) {
      if (type == "platform") {
        // uni.navigateToMiniProgram({
        // 	appId: "wx1b6a17b21753c694",
        // 	path: "/pages/index/auth",
        // 	envVersion: "trial", //正式版
        // 	// extraData: params,
        // 	success(res) {}
        // });
        uni.showModal({
          title: "提示",
          content: "即将上线,敬请期待",
          showCancel: false
        });
      } else if (type == "rescue1") {
        // uni.navigateToMiniProgram({
        //   appId: "wx1b6a17b21753c694",
        //   path: "/pages/index/auth",
        //   envVersion: "trial", //正式版
        //   // extraData: params,
        //   success(res) {}
        // });
        uni.showModal({
          title: "提示",
          content: "一键救援服务即将上线，如需服务请拨打服务热线96333",
          success: res => {
            if (res.confirm) {
              uni.makePhoneCall({
                phoneNumber: "0771-96333" //仅为示例
              });
            }
          }
        });
      }
    },
    // 小程序页面 跳转模式
    goPagesHandle(item) {
      // 根据菜单配置页面路由地址
      if (item.url) {
        uni.navigateTo({
          url: item.url
        });
        return;
      }
      // 特殊处理路由页面
      const specialType = ["afterPay"];

      if (item.type == "recharge") {
        let flag = !!this.vehicleList.length;
        let url = flag
          ? "/pagesB/rechargeBusiness/selectVehicle/index"
          : "/pagesB/rechargeBusiness/selectVehicle/index";
        uni.navigateTo({
          url: url + "?fontType=" + item.type
        });
        return;
      }
      if (specialType.includes(item.type) && !this.vehicleList.length) {
        uni.navigateTo({
          url: "/pagesB/vehicleBusiness/noVehicleList?fontType=" + item.type
        });
        return;
      }

      uni.navigateTo({
        url: "/pagesB/vehicleBusiness/vehicleList?fontType=" + item.type
      });
    },
    onMenuHandle(item) {
      // 记录搜索历史
      let arr = JSON.parse(JSON.stringify(this.searhHistory));
      arr.forEach((el, idx) => {
        if (el.name == item.name) {
          arr.splice(idx, 1);
        }
      });
      arr.unshift(item);
      uni.setStorageSync("searhHistory", arr);

      // 特勤业务 如车生活
      let type = item.type;
      let flag = skipControlHandle(item.type);
      if (!flag) return;
      if (type == "platform" || type == "rescue1") {
        this.toMiniProgram(type);
        return;
      }
      // 游客模式 无需验证登录
      if (item.validateRule == "visitor") {
        this.onMenuSkipHandle(item);
        return;
      }
      // 登录模式 无需绑定ETC用户
      if (item.validateRule == "login") {
        if (!this.validateLogin()) return;
        this.onMenuSkipHandle(item);
        return;
      }
      // ETC用户模式 需绑定ETC用户
      if (item.validateRule == "etcAccount") {
        if (!this.validateLogin()) return;
        if (!this.validateBindAccount()) return;
        this.onMenuSkipHandle(item);
        return;
      }
    }
  },
  onShow() {
    this.initList();
  }
};
</script>

<style lang="scss" scoped>
.search-apply {
  width: 100%;
  height: 100vh;
  background: #fff;
  .search-wrap {
    display: flex;
    align-items: center;
    padding: 24rpx 0 38rpx 40rpx;
    .search-box {
      display: flex;
      align-items: center;
      width: 594rpx;
      height: 62rpx;
      background: #eeeded;
      border-radius: 37rpx 37rpx 37rpx 37rpx;
      padding: 12rpx 24rpx;
      margin-right: 20rpx;
      .icon {
        margin-right: 16rpx;
      }
      &.isSearch {
        background: #fff;
        border: 1rpx solid #e7e7e7;
      }
    }
    .btn {
      font-weight: 400;
      font-size: 32rpx;
      color: #999999;
    }
  }
  .content-wrap {
    padding: 0 40rpx;
    .warp-title {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 20rpx;
    }
    .item-list {
      // display: flex;
      .info-item {
        width: 146rpx;
        height: 56rpx;
        border-radius: 38rpx 38rpx 38rpx 38rpx;
        border: 1rpx solid #e7e7e7;
        font-weight: 400;
        font-size: 28rpx;
        color: #3d3d3d;
        text-align: center;
        line-height: 56rpx;
        float: left;
        margin-right: 24rpx;
        margin-bottom: 32rpx;
      }
    }
  }
  .commonly-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .card {
      display: flex;
      width: 25%;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 22rpx 0;
      position: relative;

      .name {
        font-weight: 400;
        color: #323435;
        font-size: 26rpx;
        margin-top: 12rpx;
      }

      .editIcon {
        position: absolute;
        right: 50rpx;
        top: -10rpx;
      }
    }
  }
}
</style>