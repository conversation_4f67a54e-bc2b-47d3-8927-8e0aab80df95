<template>
  <view class="msg-list">
    <view class="scroll-box">

        <msgItem v-for="(item, idx) in msgList" :key="idx" :itemInfo="item" />
        <!-- <load-more :loadStatus="noticeLoadStatus" /> -->
    </view>

    <tLoading :isShow="isLoading" />
    <u-empty mode="data" v-if="msgList.length == 0" color="#999"></u-empty>
  </view>
</template>

<script>
import { getLoginUserInfo, getEtcAccountInfo } from "@/common/storageUtil.js";
import msgItem from "./msg-item.vue";
import tLoading from "@/components/common/t-loading.vue";
// import loadMore from "@/pagesB/components/load-more/index.vue";
import mapAPI from "@/common/api/map.js";
export default {
  components: {
    msgItem,
    tLoading
    // loadMore
  },
  data() {
    return {
      msgList: [],
      isLoading: false
    };
  },
  methods: {
    async getList() {
      let params = {
        custMastId: getEtcAccountInfo().custMastId,
        netUserId: getLoginUserInfo().userIdStr,
        // custMastId: '2860784'

      };
      let res = await this.$request.post(mapAPI.messageList, {
        data: params
      });
      if (res.code == 200 && res.data) {
        console.log(res.data);
        this.msgList = res.data;
      }
    }
  },
  onLoad() {
    this.getList();
  }
};
</script>

<style lang="scss" scoped>
.msg-list {
  padding: 20rpx;
}
</style>