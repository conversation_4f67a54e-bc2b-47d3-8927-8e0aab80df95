<template>
  <view class="parking-item">
    <view class="item-title">
      <view class="item-name">
        <image
          class="phone-icon"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/park_icon_highlight.png"
          mode=""
        ></image
        ><text>停车场</text></view
      >
      <view class="item-status">正常</view>
    </view>
    <view class="tips">停车场面积 {{info.parkingArea || 0 }} ㎡</view>
    <view class="item-info">
      <view class="item-info-line">
        <view class="item" style="margin-right:20rpx;">
          <image
            class="phone-icon"
            src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/huochecar.png"
            mode=""
          ></image
          ><text>大型货车位:</text>{{info.largeTruck || 0}}</view
        >
        <view class="item">
          <image
            class="phone-icon"
            src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/bus.png"
            mode=""
          ></image
          ><text>大型客车位:</text>{{info.largeBuses || 0}}</view
        >
      </view>
      <view class="item-info-line">
        <view class="item" style="margin-right:20rpx;">
          <image
            class="phone-icon"
            src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/smcar.png"
            mode=""
          ></image
          ><text>小型车位:</text>{{info.smallCar || 0}}</view
        >
        <view class="item">
          <image
            class="phone-icon"
            src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/dangercar.png"
            mode=""
          ></image
          ><text> 危险品运输车位:</text>{{info.dangerousCars || 0}}</view
        >
      </view>
      <view class="item-info-line">
        <view class="item">
          <image
            class="phone-icon"
            src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/chonngwucar.png"
            mode=""
          ></image
          ><text>牲畜运输车位:</text>{{info.livestockCars || 0}}</view 
        >
      </view>
    </view>

    <view class=""></view>
  </view>
</template>

<script>
export default {
  props:{
    info:{
      type:Object,
      default:()=> {}
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./item-style.scss";
.item-info {
  height: 228rpx;
}
.item-info-line {
  display: flex;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 24rpx;
  .item {
    flex: 1;
    image {
      width: 32rpx;
      height: 32rpx;
    }
    text {
      color: #333;
      font-weight: 400;
      margin: 0 30rpx 0 16rpx;
    }
  }
}
</style>