<template>
  <view class="parking-item">
    <view class="item-title">
      <view class="item-name">
        <image
          class="phone-icon"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/restaurant_icon_highlight.png"
          mode=""
        ></image
        ><text>餐饮</text></view
      >
      <view class="item-status">正常</view>
    </view>
    <view class="item-info">
      <view class="item-info-line">
        <view class="item" style="margin-right:20rpx;">
          <text>餐厅数量:</text>{{ info.restaurantCount || 0 }} 家</view
        >
        <view class="item">
          <text>餐厅面积:</text>{{ info.restaurantArea || 0 }} ㎡</view
        >
      </view>
      <view class="item-info-line">
        <view class="item" style="margin-right:20rpx;">
          <text>容纳就餐人数:</text>{{ info.accommodate || 0 }} 人</view
        >
        <view class="item">
          <text>小吃店数量:</text>{{ info.snackCount || 0 }} 家</view
        >
      </view>
      <view class="item-info-line">
        <view class="item" style="margin-right:20rpx;">
          <text>小吃种类:</text>{{ info.snackKind || "无" }}</view
        >
      </view>
      <view class="item-info-line">
        <view class="item mainCuisine" style="margin-right:20rpx;">
          <text>主要菜系:</text>
          <view
            class="tags"
            :class="colorObj[idx]"
            v-show="mainCuisineList.length > 0"
            v-for="(item, idx) in mainCuisineList"
            :key="idx"
            >{{ item }}</view
          >
          <view class="" v-show="mainCuisineList.length <= 0">{{
            info.mainCuisine || "无"
          }}</view>
        </view>
      </view>
    </view>

    <view class=""></view>
  </view>
</template>

<script>
let colorObj = {
  0: "green",
  1: "red",
  2: "blue",
  3: "orange",
  4: "old"
};
export default {
  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      colorObj
    };
  },
  computed: {
    mainCuisineList() {
      if (!this.info.mainCuisine) {
        return [];
      } else if (this.info.mainCuisine.includes("暂未营业") || this.info.mainCuisine.includes("无")) {
        return [];
      } else {
        return this.info.mainCuisine.split("、");
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./item-style.scss";
.item-info {
  height: 300rpx;
}
.item-info-line {
  display: flex;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 24rpx;
  .item-title {
    margin-bottom: 20rpx;
  }
  .item {
    flex: 1;
    image {
      width: 32rpx;
      height: 32rpx;
    }
    text {
      color: #333;
      font-weight: 400;
      margin: 0 30rpx 0 16rpx;
    }
  }
  .mainCuisine {
    display: flex;
    align-items: center;
    .tags {
      // width: 92rpx;
      padding: 0 18rpx;
      height: 48rpx;
      background: #60d966;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      text-align: center;
      line-height: 48rpx;
      color: #fff;
      margin-right: 6rpx;
      &.red {
        background: #f14957;
      }
      &.blue {
        background: #4f90ff;
      }
      &.orange {
        background: #ff9e59;
      }
      &.old {
        background: #b6e3ca;
      }
    }
  }
}
</style>