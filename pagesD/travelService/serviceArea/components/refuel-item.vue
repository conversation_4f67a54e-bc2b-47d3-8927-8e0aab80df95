<template>
  <view class="parking-item">
    <view class="item-title">
      <view class="item-name">
        <image
          class="phone-icon"
          src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/tankers_icon_highlight.png"
          mode=""
        ></image
        ><text>加油/充电设施</text></view
      >
      <view class="item-status">正常</view>
    </view>
    <view class="item-info">
      <view class="item-info-line">
        <view class="item" style="margin-right:20rpx;">
          <text>加油机数量:</text>{{ info.tankers || 0}}个</view
        >
        <view class="item"> <text>加油枪数量:</text>{{ info.fuelGuns || 0}}个</view>
      </view>
      <view class="item-info-line">
        <view class="item" style="margin-right:20rpx;">
          <text>加气枪数量:</text>{{ info.airGuns || 0}}个</view
        >
        <view class="item"> <text>充电桩数量:</text>{{ info.chargingPile || 0}}个</view>
      </view>
    </view>

    <view class=""></view>
  </view>
</template>

<script>
export default {
    props:{
    info:{
      type:Object,
      default:()=> {}
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./item-style.scss";
.item-info {
  height: 164rpx;
}
.item-info-line {
  display: flex;
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 24rpx;
  .item-title {
    margin-bottom: 20rpx;
  }
  .item {
    flex: 1;
    image {
      width: 32rpx;
      height: 32rpx;
    }
    text {
      color: #333;
      font-weight: 400;
      margin: 0 30rpx 0 16rpx;
    }
  }
}
</style>