<template>
  <view class="fee-poup">
    <u-popup
      v-model="show"
      mode="center"
      width="650rpx"
      height="80%"
      border-radius="14"
      z-index="9999"
      :closeable="false"
      close-icon-color="#58bc58"
      class="fee-poup"
    >
      <view class="content-wrapper">
        <view class="title">广西高速公路交通排障、拯救作业收费标准表</view>
        <view class="title-1">一、拖车(硬拖)收费标准</view>

        <u-table :th-style="thStyle" bg-color="#fff">
          <scroll-view scroll-x="true"  style="width: 100%;margin-bottom: 10rpx;">
            <u-tr style="position: sticky;top:0;">
              <u-th>车型</u-th>
              <u-th>车型备注</u-th>
              <u-th>车况</u-th>
              <u-th>拖车5公里收费标准（元）</u-th>
              <u-th>拖车10公里收费标准（元）</u-th>
              <u-th>拖车15公里收费标准（元）</u-th>
              <u-th>拖车20公里收费标准（元）</u-th>
              <u-th>拖车25公里收费标准（元）</u-th>
              <u-th>拖车30公里收费标准（元）</u-th>
              <u-th>拖车35公里收费标准（元）</u-th>
              <u-th>拖车40公里收费标准（元）</u-th>
              <u-th>拖车45公里收费标准（元）</u-th>
              <u-th>拖车50公里收费标准（元）</u-th>
              <u-th>拖车55公里收费标准（元）</u-th>
            </u-tr>

            <scroll-view
              scroll-y="true"
              v-if="huocheList.length > 0"
              style="width: 2780rpx;box-sizing: border-box;height:500rpx;margin-bottom: 10rpx;"
            >
              <u-tr v-for="(item, idx) in huocheList" :key="idx">
                <u-td style="background-color: #f5f6f8;">{{
                  item.faultyVehicleTypeText
                }}</u-td>
                <u-td>{{ item.vehicleDesc }}</u-td>
                <u-td>{{ item.faultyVehicleConditionText }}</u-td>
                <u-td>{{ item.fiveKmToll }}</u-td>
                <u-td>{{ item.tenKmToll }}</u-td>
                <u-td>{{ item.fifteenKmToll }}</u-td>
                <u-td>{{ item.twentyKmToll }}</u-td>
                <u-td>{{ item.twentyFiveKmToll }}</u-td>
                <u-td>{{ item.thirtyKmToll }}</u-td>
                <u-td>{{ item.thirtyFiveKmToll }}</u-td>
                <u-td>{{ item.fortyKmToll }}</u-td>
                <u-td>{{ item.fortyFiveKmToll }}</u-td>
                <u-td>{{ item.fiftyKmToll }}</u-td>
                <u-td>-</u-td>
              </u-tr>
            </scroll-view>
          </scroll-view>
        </u-table>
        <view class="title-1">备注说明:</view>
        <view class="title-1"
          >1、本标准以拯救车拖车里程5公里为基本计算单位(起步价)，5公里以内按5公里计算。5公里以上每增加5公里按收费表对应增加收费;增加的里程尾数不足5公里的部分按增加5公里计算。</view
        >
        <view class="title-2">2、收费金额按四舍五入取整至元。</view>

        <view class="title-1">二、吊车收费标准</view>
        <scroll-view scroll-x="true" style="width: 100%;margin-bottom: 10rpx;">
          <u-table :th-style="thStyle2" bg-color="#fff">
            <u-tr>
              <u-th>吊车车型</u-th>
              <u-th>车型备注</u-th>
              <u-th>出车费（元/台）</u-th>
              <u-th>台班费（元/台）</u-th>
            </u-tr>
            <u-tr v-for="(item, idx) in diaocheList" :key="idx">
              <u-td style="background-color: #f5f6f8;">{{
                item.craneTypeText
              }}</u-td>
              <u-td>{{ item.description }}</u-td>
              <u-td>{{ item.departureFee }}</u-td>
              <u-td>{{ item.shiftFee }}</u-td>
            </u-tr>
          </u-table>
        </scroll-view>
        <view class="title-1"
          >备注:吊车费由出车费和台班费组成，台班费的时间从吊车在事故地开始作业算起至将事故车吊好为止</view
        >
      </view>
      <view class="close" @click="closePoup">
        <image class="image" src="@/static/toc/close.png"></image
      ></view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      modal: {
        show: false,
        close: false,
        align: "center",
        showCancel: false,
        showConfirm: false
      },
      thStyle: {
        width: "200rpx"
      },
      thStyle2: {
        width: "300rpx"
      },
      show: false,
      huocheList: [],
      diaocheList: []
    };
  },
  methods: {
    openPoup() {
      // this.$refs.popup.open("center");
      this.modal.show = true;
      this.show = true;
    },
    closePoup() {
      this.show = false;
    },
    async init() {
      let params = {};
      let res = await this.$request.post(this.$interfaces.getTrailerCharges, {
        data: params
      });
      this.huocheList = res.data.data;
      let res2 = await this.$request.post(this.$interfaces.craneChargesList, {
        data: params
      });
      this.diaocheList = res2.data.data;
    }
  },
  created() {
    this.$nextTick(() => {
      this.init();
    });
  }
};
</script>

<style lang="scss" scoped>
.fee-poup {
  z-index: 9999;
  // position: relative;
  .content-wrapper {
    // width: 600rpx;
    // height: 1100rpx;
    // overflow-x: scroll;
    padding: 28rpx;
    padding-top: 48rpx;
    position: relative;
    .title {
      margin-bottom: 20rpx;
      text-align: center;
      font-size: 28rpx;
      font-weight: 600;
    }
    .title-1 {
      margin-bottom: 10rpx;
      font-weight: 400;
    }
    .title-2 {
      margin-bottom: 30rpx;
      font-weight: 400;
    }
  }
}
.close {
  position: fixed;
  width: 35rpx;
  height: 35rpx;
  top: 10%;
  right: 49rpx;
  z-index: 99999;
  image {
    width: 100%;
    height: 100%;
  }
}
</style>