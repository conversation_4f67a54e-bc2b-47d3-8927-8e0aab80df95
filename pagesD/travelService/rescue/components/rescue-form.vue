<template>
  <view class="rescue-form">
    <view class="btns-box">
      <view class="btn" :class="{ checkd: item.checkd }" @click="btnClick(item)" v-for="item in btns" :key="item.id">{{
        item.name }}</view>
    </view>
    <view class="tips">
      <view v-if="tips">
        {{ tips }}
      </view>
      <view v-else>
        <input class="input" placeholder-class="pla-css" placeholder="请输入" type="text" maxlength="11"
          v-model="formData.otherSouceText" />
      </view>
    </view>
    <view class="l-title">故障地点</view>
    <view class="address" v-if="recommendAddress.expressWayName">{{
      recommendAddress.expressWayName
      }}</view>
    <view class="road-num" v-if="recommendAddress.pileNo">{{
      recommendAddress.pileNo
      }}</view>
    <view class="road-direction" v-if="direction.length > 0">
      <view class="direction-item" :class="{ checkd: item.checkd }" @click="directionClick(item)"
        v-for="item in direction" :key="item.id">
        <image v-show="item.checkd" class="item-img" src="~@/pagesD/static/map/success_status.png"></image>
        <image v-show="!item.checkd" class="item-img" src="~@/pagesD/static/map/select.png"></image>
        <view class="direction-item-name">{{ item.directionShowName || item.direction_name }}</view>
      </view>
    </view>

    <view class="address-no" v-else>您当前不在交投集团运营的高速管辖范围内，如需协助请拨打电话0771-96333</view>

    <view class="form-wrap">
      <view class="input-wrapper">
        <view class="input-title">
          客户姓名
        </view>
        <input class="input" placeholder-class="pla-css" placeholder="请输入姓名" type="text" maxlength="11"
          v-model="formData.eventContactName" />
      </view>
      <view class="input-wrapper">
        <view class="input-title weui-label__require">
          联系电话
        </view>
        <input class="input" placeholder-class="pla-css" placeholder="请输入联系电话" type="number" maxlength="11"
          v-model="formData.eventContactPhone" />
      </view>
      <view class="input-wrapper">
        <view class="input-title weui-label__require">
          车牌号码
        </view>
        <view class="input" @click="showPlate">
          <view v-if="formData.carNo">
            {{ formData.carNo }}
          </view>
          <view v-else style="color:#818181;">请输入车牌号码</view>
        </view>
      </view>
      <view class="input-wrapper">
        <view class="input-title">
          车辆类型
        </view>
        <view class="input" @click="showCarTypePicker" :style="{ color: formData.carType ? '#333' : '#818181' }">
          {{ formData.carType ? formData.carTypeLabel : "请选择车辆类型" }}
        </view>
        <vehicle-type-picker :show="carTypePickerShow" :list="carTypeList" @select="confirmCarType"
          @close="carTypePickerShow = false" />
      </view>
      <view class="input-wrapper">
        <view class="input-title">
          预估费用
        </view>
        <view class="input"><text class="price" @click="openFeePoup"> 参考救援收费标准</text></view>
      </view>
      <view class="input-wrapper">
        <view class="input-title">
          现场照片
        </view>
        <view class="input auto-input">已上传{{ imgList.length }}张（限制3张）</view>
        <!-- <view class="autofill" @click="autoFill">
          上传照片
        </view> -->
      </view>
      <view class="list-item_attachment">
        <view class="upload-from">
          <view class="upload" v-for="(item, index) in imgList" @tap="ViewImage(item)" :key="index">
            <view class="upload-wrap">
              <view class="upload-wrap-bd">
                <image :src="item" class="upload-wrap__img" mode="aspectFilt">
                </image>
                <view class="upload-wrap__close" @tap.stop="delImgHandle(item, index)">
                  <image src="~@/pagesD/static/close.png" mode="" class="close">
                  </image>
                </view>
              </view>
            </view>
          </view>
          <view class="upload upload-add" v-if="imgList.length < 3">
            <view class="upload-wrap">
              <view class="upload-wrap-bd" @tap="chooseImage(item)">
                <image src="~@/pagesD/static/add_icon.png" class="upload-wrap__img">
                </image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <sideNav class="side" @toolTap="toolTap" />
    <plate-input v-if="plateShow" :plate="formData.carNo" @export="setPlate" @close="plateShow = false" />
    <!-- 图片上传组件 -->
    <cpimg ref="cpimg" @result="cpimgOk" :number="1" :fixOrientation="true" :size="500" :maxWidth="800" :ql="0.9"
      type="base64">
    </cpimg>
  </view>
</template>

<script>
import sideNav from "./side-nav.vue";
import plateInput from "@/components/uni-plate-input/uni-plate-input.vue";
import { getLoginUserInfo } from "@/common/storageUtil.js";
import cpimg from "@/components/uni-yasuo/cpimg.vue";
import VehicleTypePicker from './vehicle-type-picker.vue'
var dayjs = require("@/js_sdk/dayjs/dayjs.min.js");

export default {
  components: {
    sideNav,
    plateInput,
    cpimg,
    VehicleTypePicker
  },
  props: {
    recommendAddress: {
      type: Object,
      default: () => { }
    }
  },
  data () {
    return {
      formData: {
        carTypeLabel: "客一类",
        carType: '客一类',
        eventTypeName: "故障",
        eveventTypeId: 1
      },
      plateShow: false,
      tips: "适用情况：车辆没油、电瓶没电、发动机故障、爆胎等",
      btns: [
        {
          id: 1,
          name: "故障",
          checkd: true,
          tips: "适用情况：车辆没油、电瓶没电、发动机故障、爆胎等"
        },
        {
          id: 2,
          name: "事故",
          checkd: false,
          tips: "适用情况：车辆追尾、撞护栏、侧翻、掉边沟、自燃等"
        },
        {
          id: 3,
          name: "一键救援",
          checkd: false,
          tips:
            "适用情况：车辆因陷入沟渠、绿化带、沙地或其他环境导致无法行驶，且需动用辅助设施或多人完成救援"
        },
        { id: 4, name: "其他", checkd: false, tips: "" }
      ],
      direction: [],
      carTypeList: [],
      imgList: [],
      urlList: [],
      carTypePickerShow: false
    };
  },
  computed: {
    loginInfo () {
      return getLoginUserInfo() && Object.keys(getLoginUserInfo()).length
        ? getLoginUserInfo()
        : null;
    }
  },
  watch: {},
  methods: {
    toolTap (item) {
      if (item.type == "rescueType") {
      } else {
        uni.navigateTo({
          url: "/pagesD/travelService/rescue/rescue-order"
        });
      }
    },
    openFeePoup(){
      this.$emit('toolTap',{type:'fee'})
    },
    showPlate () {
      console.log(1111);
      this.plateShow = true;
    },
    setPlate (plate) {
      if (plate.length >= 7) this.formData.carNo = plate;
      this.plateShow = false;
    },
    showCarTypePicker () {
      this.carTypePickerShow = true
    },
    confirmCarType (item) {
      this.formData.carType = item.value
      this.formData.carTypeLabel = item.value
    },
    btnClick (item) {
      this.btns.forEach(el => {
        el.checkd = false;
      });
      item.checkd = true;
      this.formData.eveventTypeId = item.id
      this.formData.eventTypeName = item.id == 1 ? item.name : item.id == 2 ? item.name : '其他';
      this.tips = item.tips;
    },
    directionClick (item) {
      this.direction.forEach(el => {
        // el.checkd = false
        this.$set(el, "checkd", false);
      });
      // item.checkd = true
      this.$set(item, "checkd", true);
      this.formData.directionTraffic = item.direction_name;
      this.formData.directionTrafficFlag = item.id;
      console.log(item, 111);
    },
    chooseImage () {
      this.$refs.cpimg._changImg(this.sourceType);
    },
    delImgHandle (obj, idx) {
      this.imgList.splice(idx, 1);
      this.urlList.splice(idx, 1);
      console.log(this.imgList, this.urlList, 111);
    },
    ////图片压缩成功
    async cpimgOk (file) {
      // this.sendUploadFile(file);
      console.log(file, "filefilefile");
      let files = [];
      files.push(file);
      let params = {
        image: file[0]
      };
      let res = await this.$request.post(this.$interfaces.travelUploadFile, {
        data: params
      });
      console.log(res.data, "filefilefile");
      if (res.code == 200) {
        this.imgList.push(file);
        this.urlList.push(res.data.data);
      } else {
        uni.showToast({
          title: "图片上传失败！",
          duration: 1000
        });
      }
    },
    ViewImage (path) {
      console.log(path, 11);
      let newArr = [];
      newArr.push(path[0]);
      uni.previewImage({
        urls: newArr,
        fail: err => {
          console.log(err);
        }
      });
    },
    async init () {
      let params = {};
      let { data } = await this.$request.post(
        this.$interfaces.faultyVehicleType,
        {
          data: params
        }
      );
      console.log(process.env.NODE_ENV, 'envVersion');
      this.carTypeList = data.data.map(item => {
        return {
          ...item,
          label: `${item.value}${item.description ? '-' + item.description : ''}`,
          value: item.value,
          description: item.description
        };
      });
    },
    getByExpWayId (query) {
      this.$request
        .post(this.$interfaces.getByExpWayId, {
          data: query
        })
        .then(result => {
          let arr = result.data.data.directions.map(item => {
            return {
              ...item,
              checkd: false
            };
          });
          console.log(arr, 111);

          this.direction = arr;
          let defaultArr = arr.filter(item => item.direction_flag == query.pileUpDownFlag);
          this.directionClick(defaultArr[0]);
        });
    },
    postInfo () {
      let formData = {
        // ...this.formData,
        pileNoStart: this.recommendAddress.pileNo,
        loadName: this.recommendAddress.expressWayName,
        directionTraffic: this.formData.directionTraffic,
        directionTrafficFlag: this.formData.directionTrafficFlag,
        reportType: 4,
        eventSource: "DRIVER",
        eventTime: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        eventStatus: 0,
        eventTypeName: this.formData.eventTypeName,
        eventPicUrl: this.urlList.join(","),
        eventAssitInfoEntity: {
          eventContactName: this.formData.eventContactName,
          eventContactPhone: this.formData.eventContactPhone,
          eventVehicleDTOS: [
            {
              id: "",
              type: this.formData.carType,
              name: this.formData.carNo
            }
          ]
        }
      };
      if (this.formData.eveventTypeId == 4) {
        formData.otherSouceText = this.formData.otherSouceText;
      }
      return formData;
    }
  },
  created () {
    this.init();
    this.formData.eventContactPhone = this.loginInfo.loginName;
  }
};
</script>

<style lang="scss" scoped>
.rescue-form {
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  padding: 30rpx 0 0 28rpx;
  background: #fff;
  position: relative;

  .btns-box {
    display: flex;

    .btn {
      height: 58rpx;
      background: rgba(0, 102, 233, 0.13);
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #0066e9;
      line-height: 58rpx;
      margin-right: 16rpx;
      padding: 0 30rpx;
      margin-bottom: 20rpx;

      &.checkd {
        background: #0066e9;
        color: #ffffff;
      }
    }
  }

  .tips,
  .address {
    width: 600rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #818181;
    overflow: hidden;
    word-break: normal;
    word-wrap: break-word;
    margin-bottom: 20rpx;

    &-no {
      color: #0066e9;
      margin-bottom: 20rpx;
    }
  }

  .l-title {
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
    margin-bottom: 8rpx;
  }

  .road-num {
    font-weight: 400;
    font-size: 32rpx;
    color: #3d3d3d;
    margin-bottom: 40rpx;
  }

  .road-direction {
    display: flex;
  }
  .address-no{
    width: 600rpx;
  }
  .direction-item {
    min-height: 70rpx;
    background: #fff;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    margin-right: 32rpx;
    padding: 16rpx 24rpx;
    margin-bottom: 20rpx;
    border: 1rpx solid #b8b8b8;
    display: flex;
    align-items: center;

    &.checkd {
      background: #e4efff;
      border-color: rgba(0, 159, 246, 1);
    }

    .item-img {
      width: 34rpx;
      height: 34rpx;
      margin-right: 10rpx;
    }
    .direction-item-name{
      flex: 1;
    }
  }

  .form-wrap {
    .input-wrapper {
      // padding: 0 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 80rpx;
      line-height: 40rpx;
      color: rgba(16, 16, 16, 100);
      font-size: 28rpx;
      // text-align: center;
      font-family: Arial;
      background-color: $uni-bg-color;

      .input-title {
        flex: 0 0 194rpx;
        width: 194rpx;
        // text-align: left;
        font-size: 30rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #818181;
      }

      .input {
        flex: 1;

        .price {
          color: #0081ff;
        }
      }

      .auto-input {
        position: relative;
      }

      .autofill {
        position: absolute;
        right: 30rpx;
        height: 120rpx;
        line-height: 120rpx;
        padding: 0 20rpx;
        color: #0081ff;
        z-index: 98;
      }
    }
  }

  .side {
    position: absolute;
    top: 30rpx;
    right: 0;
  }
}

$uploadWidth: 166rpx;
$uploadHeight: 166rpx;

.list-item_attachment {
  width: 100%;
  overflow: hidden;
  margin-left: 2rpx;
  padding-bottom: 28rpx;
}

.upload-from {
  background-color: #ffffff;
  // padding-bottom: 16rpx;
  // margin-top: 16rpx;
  width: 100%;
  overflow: hidden;
}

.upload {
  width: 155rpx;
  float: left;
  margin-right: 32rpx;
}

.upload:nth-child(3) {
  margin-right: 0rpx;
}

.upload {
  width: $uploadWidth;
}

.upload-wrap .upload-wrap-desc {
  font-size: 26rpx;
  text-align: center;
  width: 100%;
  color: #333333;
  font-weight: 400;
  margin-top: 26rpx;
}

.upload-wrap .upload-wrap-bd {
  width: $uploadWidth;
  height: $uploadHeight;
  position: relative;
}

.upload-wrap .upload-wrap-bd .upload-wrap__img {
  width: $uploadWidth;
  height: $uploadHeight;
  border-radius: 8rpx;
}

.upload-wrap .upload-wrap-bd .upload-wrap__close {
  position: absolute;
  right: 10rpx;
  top: 10rpx;
  // padding: 0 10rpx;
  // font-size: 36rpx;
}

.upload-wrap .upload-wrap-bd .upload-wrap__close .close {
  width: 30rpx;
  height: 30rpx;
  background-size: 100%;
}
</style>