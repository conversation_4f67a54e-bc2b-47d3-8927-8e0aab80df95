<template>
  <view class="rescue">
    <view class="info-title">
      <image class="warn-img" src="~@/pagesD/static/map/warnImg.png"></image>
      请确保人员已撤离安全地带，做好安全防护！
    </view>
    <view class="location-info" v-if="location.latitude && location.longitude">
      {{ location.latitude }},{{ location.longitude }}
    </view>
    <feePoup ref="feePoup" />
    <guidePoup ref="guidePoup" />

    <MapWrapCom
      :scale="scale"
      :mapLocation="mapLocation"
      ref="MapWrapCom"
      class="MapWrapCom"
      height="692rpx"
      @updateLocation="getLoaction"
      @getAddressInfo="getAddressInfo"
    />
    <!-- 救援引导 -->
    <image
      class="rescue-img"
      @click="showGuide"
      src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/guide.png"
    ></image>
    <!-- 地图工具 -->
    <mapTool @toolTap="toolTap" class="controls-wrap" />
    <!-- 直接呼叫 -->
    <view class="phone-call" @click="callPhone">
      <view class="phone-left">
        <view class="left1">直接呼叫</view>
        <view class="left2">0771-96333</view>
      </view>
      <view class="phone-right">一键救援</view>
    </view>
    <rescueForm ref="rescueForm" :recommendAddress="recommendAddress" @toolTap="toolTap"/>
    <view class="search-btn">
      <button class="weui-btn weui-btn_primary " :class="{isGray:!isApply}"  @click="onSearchHandle">
        确认提交救援申请
      </button>
    </view>
    <!-- <view class="agreement" @click="goAgreement"
      >申请服务，即标识已阅读并同意
      <text class="txt">《道路救援服务协议》</text></view
    > -->
    <neil-modal
      :show="modal.show"
      :auto-close="modal.close"
      :align="modal.align"
      :showCancel="modal.showCancel"
      :confirm-text="modal.confirmText"
      @confirm="onConfirmHandle"
      @close="close"
    >
      <view class="message-content-wrapper">
        <view class="title">提示</view>
        <view class="msg_desc">
          <view class="msg_desc-content">
            <view style="text-align:left;margin-bottom:8rpx"
              >订单收费规则:</view
            >
            <view style="text-align:left;margin-bottom:8rpx"
              >1、订单提交3分钟内您可在线免费取消。</view
            >
            <view style="text-align:left;margin-bottom:8rpx"
              >2、订单提交超过3分钟后不支持在线取消。</view
            >
            <view style="text-align:left;margin-bottom:8rpx"
              >3、如向客服申请取消，救援服务仍将会向您收取一定的救援车辆放空费用，请您知悉。</view
            >
            <view style="text-align:left;margin-bottom:8rpx"
              >您现在确定要申请救援吗?</view
            >
          </view>
        </view>
      </view>
    </neil-modal>
  </view>
</template>

<script>
import MapWrapCom from "@/components/map/map.vue";
import mapTool from "./components/map-tool.vue";
import rescueForm from "./components/rescue-form.vue";
import feePoup from "./components/fee-poup.vue";
import guidePoup from "./components/guide-poup.vue";
import { getEtcAccountInfo,getLoginUserInfo } from "@/common/storageUtil.js";
import { checkPhone } from "@/common/util.js";
export default {
  components: {
    MapWrapCom,
    mapTool,
    rescueForm,
    feePoup,
    guidePoup
  },
  data() {
    return {
      scale: 14,
      mapLocation: {},
      recommendAddress: {},
      directionsArr: [],
      modal: {
        show: false,
        close: false,
        showCancel: true,
        align: "left",
        confirmText: "确认"
      },
      address: "",
      location: {},
      isApply:false,
      isSubmitting: false,
    };
  },
  methods: {
    toolTap(item) {
      console.log(item);
      if (item.type == "fee") {
        this.$refs.feePoup.openPoup();
      } else if (item.type == "phone") {
        this.callPhone();
      }
    },
    showGuide() {
      this.$refs.guidePoup.openPoup();
    },
    callPhone() {
      uni.makePhoneCall({
        phoneNumber: "0771-96333" //仅为示例
      });
    },
    goAgreement() {
      var callcenter = "https://portal.gxetc.com.cn/agreement/user.png";
      uni.navigateTo({
        url:
          "/pages/uni-webview/h5-webview?ownPath=" +
          encodeURIComponent(callcenter)
      });
    },
    async getLoaction(data) {
      console.log(data, "getLoaction");
      this.location = data;
      // let params = {
      //   pointLat: 23.94017116970486,
      //   pointLon: 109.32941080729167
      // };
      let params = {
        pointLat: data.latitude,
        pointLon: data.longitude
      }
      let res = await this.$request.post(
        this.$interfaces.matchPilePointByLonLat,
        {
          data: params
        }
      );
      console.log(res, "recommendAddress");

      if (res.code == 200 && res.data.data) {
        if(res.data.data.tenantId == 4520) return
        this.recommendAddress = res.data.data;
        let query = {
          expWayId: res.data.data.expressWayId,
          tenantId: res.data.data.tenantId,
          pileUpDownFlag: res.data.data.pileUpDownFlag,
          pileNo: res.data.data.pileNo
        }; 
        this.isApply = true
        this.$refs.rescueForm.getByExpWayId(query);
      }
    },
    onSearchHandle() {
      if(!this.isApply || this.isSubmitting) return
      
      let info = this.$refs.rescueForm.postInfo();
      let { eventContactPhone, eventVehicleDTOS } = info.eventAssitInfoEntity;
      if (!checkPhone(eventContactPhone)) {
        uni.showModal({
          title: "提示",
          content: "手机号码格式不正确",
          showCancel: false
        });
        return false;
      }
      if (!eventVehicleDTOS[0].name) {
        uni.showModal({
          title: "提示",
          content: "请输入车牌号码",
          showCancel: false
        });
        return;
      }

      this.modal.show = true;
    },
    async onConfirmHandle() {
      if (this.isSubmitting) {
        console.log('防重复提交：正在提交中');
        return;
      }
      this.isSubmitting = true;
      this.modal.show = false;
      
      uni.showLoading({
        mask: true,
        title: `正在提交救援申请...`
      });
      
      try {
        let info = this.$refs.rescueForm.postInfo();
        let params = {
          ...info,
          gzcxUserId: getLoginUserInfo().userIdStr,
          expressWayId: this.recommendAddress.expressWayId,
          tenantId: this.recommendAddress.tenantId,
          location: this.address,
          lat: this.location.latitude,
          lon: this.location.longitude
        };
        
        let res = await this.$request.post(this.$interfaces.eventHis, {
          data: params
        });
        uni.hideLoading();
        if (res.code == 200 && res.data.success) {
          
          await new Promise(resolve => {
            uni.showModal({
              title: "提示",
              content: "救援申请成功",
              showCancel: false,
              confirmText: "确认",
              success: res => {
                if (res.confirm) {
                  uni.navigateTo({
                    url: "/pagesD/travelService/rescue/rescue-order"
                  });
                }
                resolve();
              }
            });
          });
        } else {
          uni.showToast({
            title: res.data.desc || '提交失败',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('救援申请提交失败:', error);
        uni.showToast({
          title: '提交失败，请重试',
          icon: 'none',
          duration: 2000
        });
      } finally {
        uni.hideLoading();
        setTimeout(() => {
          this.isSubmitting = false;
        }, 1000);
      }
    },
    close() {
      this.modal.show = false;
    },
    async init() {
      let params = {};
      let res = await this.$request.post(this.$interfaces.faultyVehicleType, {
        data: params
      });

      console.log(res);
    },
    getAddressInfo(data) {
      this.address = data.result.address;
      console.log(this.address, "location");
    }
  },
  onLoad() {
    this.init();
  }
};
</script>

<style lang="scss" scoped>
.rescue {
  // width: 100%;
  // height: 100vh;
  position: relative;
  .info-title {
    font-weight: 400;
    height: 58rpx;
    font-size: 28rpx;
    color: #f82a3c;
    background: rgba(255, 46, 65, 0.1);
    padding: 12rpx 14rpx;
    display: flex;
    align-items: center;
    .warn-img {
      width: 28rpx;
      height: 28rpx;
      margin-right: 4rpx;
    }
  }
  .location-info{
    position: absolute;
    top: 60rpx;
    left: 10rpx;
    padding: 0 20rpx;
    border-radius: 4rpx;
    background: #fff;
    z-index: 10;
    
  }
  .rescue-img {
    position: absolute;
    top: 102rpx;
    left: 30rpx;
    width: 110rpx;
    height: 110rpx;
    z-index: 10;
  }
  .controls-wrap {
    position: absolute;
    right: 22rpx;
    top: 105rpx;
    z-index: 10;
  }
  .phone-call {
    width: 100%;
    height: 90rpx;
    background: #cee0ff;
    display: flex;
    .phone-left {
      flex: 1;
      padding-left: 30rpx;
      padding-top: 26rpx;
      font-weight: 400;
      font-size: 36rpx;
      color: #3d3d3d;
      display: flex;
      .left1 {
        margin-right: 22rpx;
      }
    }
    .phone-right {
      width: 194rpx;
      height: 90rpx;
      box-sizing: border-box;
      background: #4f90ff;
      font-weight: 400;
      font-size: 32rpx;
      color: #ffffff;
      font-family: PingFang SC, PingFang SC;
      text-align: center;
      line-height: 90rpx;
      border-radius: 45rpx 0 0 45rpx;
    }
  }

  .search-btn {
    display: flex;
    align-items: center;
    padding: 24rpx 54rpx;
    margin-top: 70rpx;
  }

  .weui-btn {
    flex: 1;
    margin-top: 0;
    // margin-right: 20rpx;
    background-color: #0066e9;
    &.isGray{
      background-color: #959595;
    }
  }
  .agreement {
    text-align: center;
    padding-bottom: 50rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    .txt {
      color: rgba(79, 144, 255, 1);
    }
  }
}
.message-content-wrapper {
  .title {
    text-align: center;
    font-weight: 700;
    font-size: 34rpx;
    padding: 25rpx 50rpx;
    color: rgba(0, 0, 0, 0.9);
  }

  .msg_desc {
    padding: 0 30rpx;
    color: rgba(0, 0, 0, 0.5);
    font-size: 30rpx;
    text-align: left;
  }
}
/deep/ .neil-modal__footer-left {
  border-radius: 12rpx;
  background-color: #ffffff;
  border-width: 1rpx;
  border-style: solid;
  border-color: #0066e9;
  color: #0066e9 !important;
  height: 70rpx;
  line-height: 70rpx;
}

/deep/ .neil-modal__footer-right {
  border-radius: 12rpx;
  color: #ffffff;
  background-color: #0066e9;
  height: 70rpx;
  line-height: 70rpx;
}
</style>