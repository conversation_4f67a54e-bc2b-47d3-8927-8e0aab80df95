<template>
  <cover-view class="info-wrap">
    <cover-view class="polyline-box">
      <cover-view
        class="polyline-item"
        v-for="item in lineArr"
        :key="item.lineId"
        @click="selectLine(item)"
        :class="{ active: actvieId == item.lineId }"
      >
        <cover-view class="time">{{ item.duration }}</cover-view>
        <cover-view class="distance"
          ><cover-view class="cover-text">{{ item.distance }}公里 </cover-view>
          <cover-view class="cover-text" v-if="item.toll > 0"
            >¥{{ item.toll }}</cover-view
          >
        </cover-view>
        <cover-view class="tags">
          <cover-view class="cover-text" v-if="item.tags.length <= 0"
            >大众常选</cover-view
          >
          <cover-view class="cover-text" v-else>{{
            handleTags(item.tags)
          }}</cover-view></cover-view
        >
      </cover-view>
    </cover-view>
    <cover-view class="bottom-btn" @click="openMapApp">
      <cover-iamge class="icon"></cover-iamge>开始导航
    </cover-view>
  </cover-view>
</template>

<script>
import { removeEnglish } from "../../common/utils";

export default {
  props: {
    lineArr: {
      type: Array,
      default: () => []
    },
    actvieId: {
      type: [Number, String],
      default: 0
    }
  },
  data() {
    return {};
  },
  methods: {
    selectLine(row) {
      if (this.actvieId == row.lineId) return;
      this.$emit("selectLine", row.lineId);
    },
    handleTags(tags) {
      let arr = removeEnglish(tags);
      console.log(tags, arr);
      return arr.join("·");
    },
    openMapApp() {
      this.$emit("openMapApp");
    }
  }
};
</script>

<style lang="scss" scoped>
.info-wrap {
  width: 100%;
  height: 296rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 0rpx 0rpx;
  padding: 20rpx;
  box-sizing: border-box;
  .bottom-btn {
    width: 342rpx;
    height: 76rpx;
    background: #0066e9;
    border-radius: 52rpx 52rpx 52rpx 52rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #ffffff;
    text-align: center;
    line-height: 76rpx;
    margin: 0 auto;
  }
  .polyline-box {
    display: flex;
    box-sizing: border-box;
    margin-bottom: 30rpx;
    border-radius: 0rpx 16rpx 0rpx 0rpx;
    .polyline-item {
      flex: 1;
      background: rgba(234, 241, 246, 0.5);
      // background-image: linear-gradient(180deg,  #eaf1f6 0%, #ffffff 100%);
      padding: 24rpx 20rpx 0 20rpx;
      color: #333333;
      text-align: center;
      &.active {
        color: #4f90ff;
        background: #ffffff;
      }
      .time {
        font-size: 32rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        margin-bottom: 16rpx;
      }
      .distance {
        font-weight: 400;
        font-size: 24rpx;
        // margin-bottom: 16rpx;
      }
      .tags {
        font-size: 24rpx;
      }
    }
    .cover-text {
      display: inline-block;
    }
  }
}
</style>