<template>
  <cover-view class="location-picker">
    <cover-view class="location-wrap">
      <cover-view class="arrow-wrap">
        <cover-view class="circle green"> </cover-view>
        <cover-view class="arrow-line"> </cover-view>
        <cover-view class="circle red"> </cover-view>
      </cover-view>
      <cover-view class="input-container">
        <!-- <input
        type="text"
        v-model="startPoint.name"
        placeholder="请输入起点"
        class="location-input"
        @click="chooseLocation('startPoint')"
        disabled
      /> -->
        <cover-view
          class="search-form-map"
          @click="chooseLocation('startPoint')"
        >
          <!-- <cover-view class="title">ETC卡号</cover-view> -->
          <cover-view class="value" v-if="startPoint.name">{{
            startPoint.name
          }}</cover-view>
          <cover-view class="label" v-else>请输入起点</cover-view>
        </cover-view>
        <cover-view class="search-form-line"></cover-view>
        <!-- <input
        type="text"
        v-model="endPoint.name"
        placeholder="请输入终点"
        @click="chooseLocation('endPoint')"
        class="location-input"
      /> -->
        <cover-view class="search-form-map" @click="chooseLocation('endPoint')">
          <!-- <cover-view class="title">ETC卡号</cover-view> -->
          <cover-view class="value" v-if="endPoint.name">{{
            endPoint.name
          }}</cover-view>
          <cover-view class="label" v-else>请输入终点</cover-view>
        </cover-view>
      </cover-view>
      <cover-view class="interchange-img" @click="swapLocations">
        <cover-image
          class="cover-image"
          src="~@/pagesD/static/map/change.png"
        ></cover-image>
      </cover-view>
      <cover-view>
        <button class="btn-item" @click="searchRoute">
          搜索
        </button>
      </cover-view>
    </cover-view>
  </cover-view>
</template>  
  
<script>
export default {
  data() {
    return {
      startPoint: {},
      endPoint: {}
    };
  },
  methods: {
    // 起点终点调换
    swapLocations() {
      let temp = this.startPoint;
      this.startPoint = this.endPoint;
      this.endPoint = temp;
    },
    // 选择地点
    chooseLocation(type) {
      uni.chooseLocation({
        success: res => {
          console.log(res, 123);
          this[type] = res;
        },
        fail(err) {
          console.log(err);
        }
      });
    },
    // 查找路线
    searchRoute() {
      let params = {
        startPoint: this.startPoint,
        endPoint: this.endPoint
      };
      console.log(params);
      if (!params.startPoint.latitude || !params.endPoint.latitude) return;

      console.log(this.startPoint, this.endPoint, "666666");
      this.$emit("searchRoute", params);
    },
    updateLocation(location) {
      // let location = uni.getStorageSync("location");
      this.startPoint = {
        ...location,
        name: "当前位置"
      };
    }
  }
};
</script>  
  
<style scoped lang="scss">
.location-picker {
  // width: 100%;
  height: 193rpx;
  box-sizing: border-box;
  padding: 28rpx 20rpx;
  background-color: #fff;
  box-shadow: 0rpx 8rpx 10rpx 0rpx rgba(0, 0, 0, 0.09);
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  .location-wrap {
    width: 100%;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    border-radius: 10rpx;
    height: 137rpx;
    background: #f8f8f8;
    padding: 18rpx;
    border: 1rpx solid #e2e2e2;
    .arrow-wrap {
      width: 40rpx;
      // height: 100%;
      // margin: 0 15rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      .circle {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        &.green {
          background: #00d17c;
        }
        &.red {
          background: #ff5454;
        }
      }
      .arrow-line {
        width: 4rpx;
        height: 34rpx;
        background: #e2e2e2;
        margin: 10rpx 0;
        border-radius: 46rpx 46rpx 46rpx 46rpx;
      }
    }
    .input-container {
      width: 480rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      border-radius: 8rpx;
      .search-form-map {
        width: 100%;
        font-size: 28rpx;
        font-weight: 400;
        padding-left: 8rpx;
        .value {
          color: #333333;
        }
        .label {
          color: #959595;
        }
      }
      .search-form-line {
        width: 100%;
        height: 1rpx;
        background: #e2e2e2;
        margin: 16rpx 0;
      }
      margin-right: 15rpx;
    }
    .interchange-img {
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 24rpx 0 15rpx;
      // margin-right: 24rpx;
      .cover-image {
        width: 29rpx;
        height: 25rpx;
      }
    }

    .btn-item {
      width: 84rpx;
      height: 46rpx;
      background: #0066e9;
      color: #ffffff;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      line-height: 46rpx;
      text-align: center;
      font-size: 24rpx;
    }
  }
}
</style>