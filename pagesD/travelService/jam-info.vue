<template>
  <view class="jam-info">
    <MapWrapCom :polyline="polyline" :scale="scale" :markers="markers" :mapLocation="mapLocation"
      :includePoints="includePoints" enableTraffic showLocationErrorModal ref="MapWrapCom"
      :height="`calc(100% - ${isIos ? '180' : '120'}rpx)`" @onmarkertap="onmarkertap" @updateLocation="updateLocation"
      @onpolylinetap="onpolylinetap" />
    <!-- 地点搜索 -->
    <AddressSearch class="address" ref="addressSearch" @searchRoute="searchRoute" />
    <!-- 缩放控制 -->
    <progressBar class="progress-box" :pageScale="scale" @setScaleNum="setScaleNum" />
    <!-- 地图工具 -->
    <mapTool @toolTap="toolTap" class="controls-wrap" pageType="jam" />
    <!-- 底部信息 -->
    <bottomInfo class="info" :class="isIos ? '' : 'isIos'" :infoType="markerInfoType" v-show="showType == 'marker'"
      :infoData="infoData" @closeInfo="closeInfo" />
    <polylineInfo :lineArr="lineArr" @selectLine="selectLine" :actvieId="actvieId" @openMapApp="openMapApp"
      v-show="showType == 'polyline'" class="info" />
    <view class="bottom" style="height:120rpx">
      <tabbar current="spread" />
    </view>
  </view>
</template>

<script>
import MapWrapCom from "@/components/map/map.vue";
import AddressSearch from "./components/map-search.vue";
import mapTool from "@/components/map/map-tool.vue";
import progressBar from "./components/progress-bar.vue";
import bottomInfo from "./components/bottom-info.vue";
import polylineInfo from "./components/polyline-info.vue";
import tabbar from "@/components/base/tab-bar/index.vue";

let markersTemp = {
  zIndex: "1",
  rotate: 0,
  width: 30,
  height: 30,
  anchor: {
    x: 0.5,
    y: 1
  }
};
export default {
  components: {
    MapWrapCom,
    AddressSearch,
    mapTool,
    progressBar,
    bottomInfo,
    polylineInfo,
    tabbar
  },
  data () {
    return {
      markers: [],
      scale: 15,
      polyline: [],
      latitude: 22.77849,
      longitude: 108.40826,
      testMarkers: [
        {
          id: 1,
          latitude: 22.77849,
          longitude: 108.40826,
          title: "优惠路段",
          type: "road",
          zIndex: "1",
          iconPath:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/road_light.png",
          rotate: 0,
          width: 30,
          height: 30,
          anchor: {
            x: 0.5,
            y: 1
          }
          // callout: {
          //   content: "优惠路段 阜通东大街6号",
          //   color: "#00BFFF",
          //   fontSize: 10,
          //   borderRadius: 4,
          //   borderWidth: 1,
          //   borderColor: "#333300",
          //   bgColor: "#CCFF99",
          //   padding: 5,
          //   display: "ALWAYS"
          // }
        },
        {
          id: 2,
          latitude: 22.803526,
          longitude: 108.42291,
          title: "拥堵",
          type: "jam",
          zIndex: "1",
          iconPath:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/jam_light.png",
          width: 30,
          height: 30,
          anchor: {
            x: 0.5,
            y: 1
          }
          // callout: {
          //   content: "拥堵\n天安门",
          //   color: "#00BFFF",
          //   fontSize: 12,
          //   borderRadius: 10,
          //   borderWidth: 2,
          //   borderColor: "#333300",
          //   bgColor: "#CCFF11",
          //   padding: 5,
          //   display: "ALWAYS"
          // }
        },
        {
          id: 3,
          latitude: 22.806322,
          longitude: 108.443842,
          title: "施工",
          type: "work",
          zIndex: "1",
          iconPath:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/sg_light.png",
          rotate: 0,
          width: 30,
          height: 30,
          anchor: {
            x: 0.5,
            y: 1
          }
          // callout: {
          //   content: "施工",
          //   color: "#00BFFF",
          //   fontSize: 10,
          //   borderRadius: 4,
          //   borderWidth: 1,
          //   borderColor: "#333300",
          //   bgColor: "#CCFF99",
          //   padding: 5,
          //   display: "ALWAYS"
          // }
        },
        {
          id: 4,
          latitude: 22.789098,
          longitude: 108.425283,
          title: "事件",
          type: "event",
          zIndex: "1",
          iconPath:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/event_light.png",
          rotate: 0,
          width: 30,
          height: 30,
          anchor: {
            x: 0.5,
            y: 1
          }
          // callout: {
          //   content: "事件",
          //   color: "#00BFFF",
          //   fontSize: 10,
          //   borderRadius: 4,
          //   borderWidth: 1,
          //   borderColor: "#333300",
          //   bgColor: "#CCFF99",
          //   padding: 5,
          //   display: "ALWAYS"
          // }
        }
      ],
      dataMarkers: [],
      mapLocation: {},
      includePoints: [],
      lineArr: [],
      actvieId: 0,
      drivingPoint: {},
      showType: "", // polyline - 路线导航 ，marker - 点信息
      markerInfoType: "road",
      infoData: {},
      optionsInfo: null,
      roadCheck: true
    };
  },
  computed: {
    isIos () {
      console.log(uni.getSystemInfoSync().platform === "ios", "1211212");
      return uni.getSystemInfoSync().platform === "ios";
    }
  },
  methods: {
    addMarkers (type) {
      let markers = this.dataMarkers.filter(item => item.type == type);
      this.markers.push(...markers);
    },
    deleteMarkers (type) {
      this.markers = this.markers.filter(item => item.type != type);
    },
    addPolyline (road) {
      let data = road || this.roadData;
      let lineArr = data.map((item, idx) => {
        // console.log(item, "item");
        var coors = JSON.parse(item.polyline);
        let pl = [];
        // //坐标解压（返回的点串坐标，通过前向差分进行压缩）
        // var kr = 10000000;
        // for (var i = 2; i < coors.length; i++) {
        //   coors[i] = Number(coors[i - 2]) + Number(coors[i]) / kr;
        // }
        //将解压后的坐标放入点串数组pl中
        for (var i = 0; i < coors.length; i += 2) {
          pl.push({ latitude: coors[i], longitude: coors[i + 1] });
        }
        let polyline = {
          id: item.id,
          points: pl,
          lately: idx == 0 ? true : false,
          type: "road",
          color: idx == 0 ? "#FDD925" : "#58c16c",
          width: 4,
          borderColor: "#2f693c",
          borderWidth: 1
        };
        // console.log(pl, "plpl");
        let distance =
          item.distance >= 1000000
            ? (item.distance / 1000).toFixed(0)
            : (item.distance / 1000).toFixed(1);
        // 将分钟转换为小时和分钟
        const hours = Math.floor(item.duration / 60); // 取整
        const remainingMinutes = item.duration % 60; // 余数
        let duration = `${hours >= 1 ? hours + "小时" : ""
          }${remainingMinutes}分钟`;
        return {
          lineId: idx,
          polyline,
          distance,
          duration
        };
      });
      lineArr.forEach(item => {
        // 绘制路线
        // console.log(item, 222);
        setTimeout(() => {
          this.polyline.push(item.polyline);
        });
      });
    },
    clearPolyline () {
      this.polyline = this.polyline.filter(
        item => item.level == "abovebuildings"
      );
    },
    setScaleNum (scale) {
      this.scale = scale;
    },
    toolTap (item) {
      if (item.type == "road") {
        //屏蔽优惠路段
        return
        if (item.checkd) {
          this.addPolyline();
        } else {
          this.roadCheck = false;
          this.clearPolyline();
        }
      } else {
        if (item.checkd) {
          this.addMarkers(item.type);
        } else {
          this.deleteMarkers(item.type);
        }
      }
    },
    onmarkertap (e) {
      // console.log(e.target);
      this.markers.forEach(item => {
        if (item.id == e.target.markerId) {
          item.width = 60;
          item.height = 60;
          if (item.type) {
            this.markerInfoType = item.type;
            this.showType = "marker";
            this.infoData = item;
          }
        } else {
          item.width = 30;
          item.height = 30;
        }
      });
    },
    onpolylinetap (e) {
      this.roadData.forEach(item => {
        if (item.id == e.target.polylineId) {
          this.markerInfoType = item.type;
          this.infoData = item;
        }
      });
      this.showType = "marker";
      this.selectLineLight(e.target.polylineId);
    },
    selectLineLight (polylineId) {
      this.polyline.forEach(item => {
        if (item.id == polylineId && item.type == "road") {
          console.log(item);
          item.color = "#4F90FF";
        } else {
          if (!item.type) return
          item.color = item.lately ? "#FDD925" : "#58c16c";
        }
      });
    },
    searchRoute (value) {
      let { name, longitude, latitude } = value.endPoint;
      let location = {
        longitude,
        latitude,
        destination: name
      };
      this.polyline = [];
      this.driving(value);
      this.drivingPoint = location;
    },
    /**
     * 路线规划
     */
    driving (value) {
      var _this = this;
      let key = this.$store.state.mapKey;
      let startPointStr = `${value.startPoint.latitude},${value.startPoint.longitude}`;
      let endPointStr = `${value.endPoint.latitude},${value.endPoint.longitude}`;
      uni.request({
        //地图WebserviceAPI驾车路线规划接口 请求路径及参数（具体使用方法请参考开发文档）
        url: `https://apis.map.qq.com/ws/direction/v1/driving?key=${key}&from=${startPointStr}&to=${endPointStr}&get_mp=1`,
        success (res) {
          var result = res.data.result;
          let routes = result.routes;

          let lineArr = routes.map((item, idx) => {
            var coors = item.polyline,
              pl = [];
            //坐标解压（返回的点串坐标，通过前向差分进行压缩）
            var kr = 1000000;
            for (var i = 2; i < coors.length; i++) {
              coors[i] = Number(coors[i - 2]) + Number(coors[i]) / kr;
            }
            //将解压后的坐标放入点串数组pl中
            for (var i = 0; i < coors.length; i += 2) {
              pl.push({ latitude: coors[i], longitude: coors[i + 1] });
            }
            let polyline = {
              points: pl,
              color: idx > 0 ? "#a3ddc7" : "#58c16c",
              width: 6,
              arrowLine: true,
              borderColor: idx > 0 ? "#88b4a3" : "#2f693c",
              borderWidth: 1,
              level: "abovebuildings"
            };
            let distance =
              item.distance >= 1000000
                ? (item.distance / 1000).toFixed(0)
                : (item.distance / 1000).toFixed(1);
            // 将分钟转换为小时和分钟
            const hours = Math.floor(item.duration / 60); // 取整
            const remainingMinutes = item.duration % 60; // 余数
            let duration = `${hours >= 1 ? hours + "小时" : ""
              }${remainingMinutes}分钟`;
            return {
              lineId: idx,
              polyline,
              distance,
              duration,
              tags: item.tags,
              toll: item.toll
            };
          });
          _this.lineArr = JSON.parse(JSON.stringify(lineArr));
          _this.actvieId = 0;
          lineArr.reverse();

          lineArr.forEach(item => {
            // 绘制路线
            _this.polyline.push(item.polyline);
          });

          //标点-起点，终点
          let startMarker = {
            id: 9999,
            latitude: value.startPoint.latitude,
            longitude: value.startPoint.longitude,
            iconPath:
              "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/start-point.png",
            width: 30,
            height: 30
          };

          let endMarker = {
            id: 19999,
            latitude: value.endPoint.latitude,
            longitude: value.endPoint.longitude,
            iconPath:
              "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/toc/end-point.png",
            width: 30,
            height: 30
          };

          _this.markers = _this.markers.filter(
            item => item.id != 9999 && item.id != 19999
          );
          _this.markers.push(startMarker, endMarker);
          // 缩放，包含起点终点
          _this.includePoints = [startMarker, endMarker];

          _this.showType = "polyline";
          //屏蔽优惠路段
          // setTimeout(() => {
          //   _this.addPolyline();
          // });
        }
      });
    },
    // 打开外部地图
    openMapApp () {
      this.$refs.MapWrapCom.openMapApp(this.drivingPoint);
    },
    updateLocation (location) {
      this.$refs.addressSearch.updateLocation(location);
      this.initMapInfo(location);
    },
    selectLine (id) {
      this.actvieId = id;
      let arr = JSON.parse(JSON.stringify(this.lineArr));
      let selectPolylineArr = arr
        .filter(item => item.lineId !== id)
        .concat(
          arr.filter(item => {
            if (item.lineId === id) {
              item.polyline.color = "#58c16c";
              item.polyline.borderColor = "#2f693c";
            } else {
              item.polyline.color = "#a3ddc7";
              item.polyline.borderColor = "#88b4a3";
            }
            return item.lineId === id;
          })
        )
        .map(el => el.polyline);
      this.polyline = selectPolylineArr;
      if (this.roadCheck) {
        //屏蔽优惠路段
        // this.addPolyline();
      }
    },
    closeInfo () {
      this.showType = "";
    },
    // 获取优惠路段
    getRoad (location) {
      return new Promise((resolve, reject) => {
        let params = {
          latitude: location.latitude,
          longitude: location.longitude,
          pageNo: 1,
          pageSize: 9999
        };
        this.$request
          .post(this.$interfaces.getMapRoad, {
            data: params
          })
          .then(res => {
            let { data } = res;
            if (data.length) {
              this.roadData = data.map(item => {
                return {
                  ...item,
                  type: "road"
                };
              });
              console.log(this.roadData, "roadData");
              this.addPolyline(data);
              resolve();
            }
          });
      });
    },
    // 获取事件
    getEvent (location) {
      return new Promise((resolve, reject) => {
        let params = {
          latitude: location.latitude,
          longitude: location.longitude,
          pageNo: 1,
          pageSize: 9999
        };
        this.$request
          .post(this.$interfaces.getMapEvent, {
            data: params
          })
          .then(res => {
            let { data } = res;
            let markers = data.map(item => {
              return {
                ...markersTemp,
                ...item,
                id: item.id,
                latitude: item.lat,
                longitude: item.lon,
                title: item.loadName,
                type: "event",
                iconPath:
                  "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/event_light.png"
              };
            });
            this.dataMarkers.push(...markers);
            this.markers.push(...markers);
            resolve();
          });
      });
    },
    // 获取拥堵信息
    getJamInfo (location) {
      return new Promise((resolve, reject) => {
        let params = {
          latitude: location.latitude,
          longitude: location.longitude,
          pageNo: 1,
          pageSize: 9999
        };
        this.$request
          .post(this.$interfaces.getMapJam, {
            data: params
          })
          .then(res => {
            let { data } = res;
            let markers = data.map(item => {
              return {
                ...markersTemp,
                ...item,
                id: item.id,
                latitude: item.lat,
                longitude: item.lng,
                title: item.congestSourceArea,
                type: "jam",
                iconPath:
                  "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/jam_light.png"
              };
            });
            this.dataMarkers.push(...markers);
            this.markers.push(...markers);
            resolve();
          });
      });
    },
    // 获取施工
    getWork (location) {
      return new Promise((resolve, reject) => {
        let params = {
          latitude: location.latitude,
          longitude: location.longitude,
          pageNo: 1,
          pageSize: 9999
        };
        this.$request
          .post(this.$interfaces.getMapWork, {
            data: params
          })
          .then(res => {
            let { data } = res;
            let markers = data.map(item => {
              return {
                ...markersTemp,
                ...item,
                id: item.id,
                latitude: item.startLatitude,
                longitude: item.startLongitude,
                title: item.highspeedName,
                type: "work",
                iconPath:
                  "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/sg_light.png"
              };
            });
            console.log(data, 'getWork');
            this.dataMarkers.push(...markers);
            this.markers.push(...markers);
            resolve();
          });
      });
    },
    async initMapInfo (location) {
      uni.showLoading({
        title: `地图数据加载中`
      });
      try {
        // await this.getRoad(location);
        await this.getEvent(location);
        await this.getJamInfo(location);
        await this.getWork(location);
        uni.hideLoading();
      } catch (err) {
        uni.hideLoading();
      }

      if (this.optionsInfo) {
        // let arr = this.roadData.concat(this.dataMarkers);
        //去掉优惠路段
        let arr = this.dataMarkers;
        arr.forEach(item => {
          if (item.id == this.optionsInfo.id) {
            if (item.type != "road") {
              this.onmarkertap({
                target: {
                  markerId: item.id
                }
              });
              return;
            } else {
              this.selectLineLight(item.id)
            }
            this.markerInfoType = item.type;
            this.infoData = item;
          }
        });
        console.log('this.optionsInfo', this.optionsInfo)
        if (this.optionsInfo.lon && this.optionsInfo.lat) {
          setTimeout(() => {
            this.mapLocation = {
              longitude: this.optionsInfo.lon,
              latitude: this.optionsInfo.lat
            }
          }, 1000);
        } else {
          let locationChange = uni.getStorageSync("location");
          setTimeout(() => {
            this.mapLocation = locationChange
          }, 1000);
        }
        this.showType = "marker";
        // this.scale = 6;
      }
    }
  },
  onLoad (options) {
    if (options.infoType) {
      this.optionsInfo = {
        infoType: options.infoType,
        id: options.id,
        lon: options.lon,
        lat: options.lat,
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.jam-info {
  position: relative;
  height: 100%;

  .controls-wrap {
    position: absolute;
    right: 24rpx;
    top: 238rpx;
  }

  .address {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
  }

  .progress-box {
    position: absolute;
    top: 259rpx;
    left: 34rpx;
  }

  .info {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 180rpx;

    &.isIos {
      bottom: 120rpx;
    }
  }
}
</style>