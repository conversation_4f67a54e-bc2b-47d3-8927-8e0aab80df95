<template>
  <view class="policy-page">
    <scroll-view
      :scroll-top="scrollTop"
      :style="'height:' + (windowHeight - 10) + 'px;'"
      scroll-y="true"
      class="scroll-Y"
      :lower-threshold="lowerThreshold"
      @scrolltoupper="upper"
      @scrolltolower="scrolltolower"
      @scroll="scroll"
    >
      <view class="list">
        <view
          class="list-item"
          v-for="(item, idx) in policyList"
          :key="idx"
          @click="toDetail(item)"
        >
          <view class="item-left">
            <image :src="item.coverPic" class="enery-img" mode="widthFix"></image>
          </view>
          <view class="item-right">
            <view class="item-class"> {{ classTypeObj[item.classType] }}</view>
            <view class="item-title"> {{ item.titleName }}</view>
            <view class="item-line"> </view>
            <view class="item-time">更新时间：{{ item.publishTime }} </view>
          </view>
        </view>
      </view>
      <!-- <load-more :loadStatus="noticeLoadStatus" /> -->
    </scroll-view>
  </view>
</template>

<script>
import loadMore from "@/pagesD/components/load-more/index.vue";

export default {
  components: {
    loadMore
  },
  data() {
    return {
      flag: false,
      windowHeight: this.windowHeight,
      formData: {
        classType: "0", //资讯类型 
        category: "2", // 一级分类
        pageNum: 1,
        pageSize: 10
      },
      noticeLoadStatus: 0,
      scrollTop: 0,
      old: {
        scrollTop: 0
      },
      lowerThreshold: 120,
      policyList: [
        // {
        //   classType: "收费标准",
        //   titleName: "广西收费公路货车计费方式",
        //   publishTime: "2024.01.03",
        //   image: "../../../pagesD/static/map/money.png"
        // },
        // {
        //   classType: "品种目录",
        //   titleName: "鲜活农产品品种目录",
        //   publishTime: "2024.01.03",
        //   image: "../../../pagesD/static/map/pesticide.png"
        // },
        // {
        //   classType: "收费标准",
        //   titleName: "拖车 (硬拖)收费标准",
        //   publishTime: "2024.01.03",
        //   image: "../../../pagesD/static/map/truck.png"
        // }
      ],
      classTypeObj:{
        21:'高速收费',
        22:'农产品种类'
      }
    };
  },
  methods: {
    getInformationList() {
      this.noticeLoadStatus = 1;
      let params = {
        ...this.formData
      };
      this.$request
        .post(this.$interfaces.informationList, {
          data: params
        })
        .then(res => {
          if (res.code == 200) {
            console.log(res.data.data);
            let result = res.data?.data || [];
            if (res.data && res.data.data.length) {
              this.policyList = this.policyList.concat(result);
            } else {
              this.noticeLoadStatus = 3;
              this.flag = true;
            }
            if (res.data && this.policyList.length == res.data.page.total) {
              this.noticeLoadStatus = 3;
              this.flag = true;
            }
          } else {
            this.noticeLoadStatus = 2;
          }
        })
        .catch(err => {
          console.log(err);
          this.noticeLoadStatus = 2;
        });
    },
    scrolltolower: function(e) {
      console.log(e);
      if (this.flag) return;
      let self = this;

      setTimeout(function() {
        self.formData.pageNum = self.formData.pageNum + 1;
        self.getInformationList();
      }, 500);
    },
    scroll: function(e) {
      this.old.scrollTop = e.detail.scrollTop;
    },
    toDetail(row) {
      // articleType 1-富文本页面、2-链接跳转
      if (row.articleType == "2") {
        uni.navigateTo({
          url:
            "/pages/uni-webview/uni-webview?ownPath=" +
            encodeURIComponent(row.articleUrl)
        });
      }
      if (row.articleType == "1") {
        console.log(123123123);
        uni.navigateTo({
          url: `/pagesC/infoBusiness/detail?informationId=${row.informationId}&category=2`
        });
      }
    }
  },
  onLoad() {
    this.getInformationList();
  }
};
</script>

<style lang="scss" scoped>
.policy-page {
  padding: 20rpx;
  .list-item {
    width: 710rpx;
    // height: 241rpx;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    padding: 32rpx 30rpx;
    display: flex;
    margin-bottom: 20rpx;
    .item-left {
      width: 94rpx;
      height: 94rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .item-right {
      flex: 1;
      padding-left: 20rpx;
      box-sizing: border-box;
      .item-class {
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        margin-bottom: 10rpx;
      }
      .item-title {
        font-weight: 500;
        font-size: 36rpx;
        color: #333333;
        margin-bottom: 20rpx;
      }
      .item-line {
        width: 536rpx;
        height: 1rpx;
        background: #f0f0f0;
        margin-bottom: 18rpx;
      }
      .item-time {
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
      }
    }
  }
}
</style>